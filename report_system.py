import os
import time
import math
import random  # For random ID generation and Cobb angle simulation
from datetime import datetime
import tkinter as tk
from tkinter import messagebox
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
import sqlite3
import shutil
from user_database import get_current_user_id, is_authenticated

class ReportGenerator:
    def __init__(self, patient_name):
        """Initialize report generator with patient information"""
        self.patient_name = patient_name

        # Generate consistent patient ID based on name instead of timestamp
        self.patient_id = self.get_or_create_patient_id(patient_name)

        self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.timestamp_short = datetime.now().strftime("%Y%m%d%H%M%S")

        # Create reports directory if it doesn't exist
        os.makedirs("reports", exist_ok=True)

        # Set filename for report with timestamp to ensure uniqueness
        self.filename = f"reports/{self.patient_id}_{self.patient_name.replace(' ', '_')}_{self.timestamp_short}.pdf"

    def generate_spine_ascii_art(self, cobb_angle, is_double_curve):
        """Generate ASCII art visualization of the spine based on Cobb angle and curve type"""
        # Normalize the Cobb angle for visualization (max effect at 45 degrees)
        max_angle = 45.0
        normalized_angle = min(cobb_angle / max_angle, 1.0)

        # Characters to represent the spine
        spine_chars = ['|', '/', '\\', '~']

        if cobb_angle < 5:  # Normal spine
            return "| | | | | | | | |"

        # Generate the spine visualization
        if is_double_curve:  # S-curve (double curve)
            # Create an S-shaped curve
            curve = []
            for i in range(9):
                pos = (i / 8.0) * 2 * 3.14159  # 0 to 2π
                # Use sine wave for S-curve
                offset = int(normalized_angle * 4 * math.sin(pos))

                # Choose character based on position in the curve
                if offset > 0:
                    char = spine_chars[1]  # /
                elif offset < 0:
                    char = spine_chars[2]  # \
                else:
                    char = spine_chars[0]  # |

                # Add spaces for offset
                spaces = " " * abs(offset)
                if offset >= 0:
                    curve.append(spaces + char)
                else:
                    curve.append(spaces + char)

            return " ".join(curve)
        else:  # C-curve (single curve)
            # Create a C-shaped curve
            curve = []
            for i in range(9):
                pos = (i / 8.0) * 2 - 1  # -1 to 1
                # Use quadratic function for C-curve
                offset = int(normalized_angle * 4 * (pos * pos) * (1 if pos > 0 else -1))

                # Choose character based on position in the curve
                if offset > 0:
                    char = spine_chars[1]  # /
                elif offset < 0:
                    char = spine_chars[2]  # \
                else:
                    char = spine_chars[0]  # |

                # Add spaces for offset
                spaces = " " * abs(offset)
                if offset >= 0:
                    curve.append(spaces + char)
                else:
                    curve.append(spaces + char)

            return " ".join(curve)

    def get_or_create_patient_id(self, patient_name):
        """Get existing patient ID or create a new one if it doesn't exist"""
        try:
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute('''CREATE TABLE IF NOT EXISTS patients
                             (patient_id TEXT PRIMARY KEY, patient_name TEXT,
                              first_visit TEXT, last_visit TEXT)''')

            # Check if patient already exists
            cursor.execute("SELECT patient_id FROM patients WHERE patient_name = ?", (patient_name,))
            result = cursor.fetchone()

            if result:
                # Return existing patient ID
                return result[0]
            else:
                # Create new patient ID - use first 3 letters of name + random number
                if patient_name and len(patient_name) > 0:
                    name_part = ''.join(e for e in patient_name.split()[0] if e.isalnum())[:3].upper()
                    if not name_part:  # If name has no alphanumeric chars
                        name_part = "PT"
                else:
                    name_part = "PT"

                new_id = f"{name_part}{random.randint(1000, 9999)}"

                # Insert new patient
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                cursor.execute('''INSERT INTO patients
                                 (patient_id, patient_name, first_visit, last_visit)
                                 VALUES (?, ?, ?, ?)''',
                               (new_id, patient_name, current_time, current_time))

                conn.commit()
                return new_id
        except Exception as e:
            print(f"Error getting/creating patient ID: {str(e)}")
            # Fallback to old method if database fails
            return f"PT{int(time.time())}"
        finally:
            if conn:
                conn.close()

    @staticmethod
    def delete_report(report_path):
        """Delete a report from the database and file system

        Args:
            report_path: The full path to the report file

        Returns:
            bool: True if the report was deleted successfully, False otherwise
        """
        try:
            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None

            if not user_id:
                print("No user authenticated, cannot delete report")
                return False

            # Connect to the database
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Check if the reports table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
            table_exists = cursor.fetchone() is not None

            if not table_exists:
                print("Reports table does not exist, creating it")
                cursor.execute('''CREATE TABLE IF NOT EXISTS reports
                                (report_id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 patient_id TEXT,
                                 report_path TEXT,
                                 timestamp TEXT,
                                 user_id TEXT)''')
                conn.commit()

            # Check if the report exists in the database and belongs to the current user
            cursor.execute(
                "SELECT report_path FROM reports WHERE report_path = ? AND user_id = ?",
                (report_path, user_id)
            )

            result = cursor.fetchone()
            if result:
                # Delete the report from the database
                cursor.execute(
                    "DELETE FROM reports WHERE report_path = ? AND user_id = ?",
                    (report_path, user_id)
                )
                print(f"Deleted report from database: {report_path}")
            else:
                print(f"Report {report_path} not found in database, will still delete the file if it exists")

            conn.commit()
            conn.close()

            # Delete the file from the file system
            if os.path.exists(report_path):
                os.remove(report_path)
                print(f"Deleted report file: {report_path}")

                # Also delete any enhanced image files associated with this report
                report_dir = os.path.dirname(report_path)
                report_filename = os.path.basename(report_path)
                report_name = os.path.splitext(report_filename)[0]

                # Look for enhanced image files with similar names
                for file in os.listdir(report_dir):
                    if file.startswith(f"enhanced_") and report_name in file:
                        enhanced_path = os.path.join(report_dir, file)
                        if os.path.exists(enhanced_path):
                            os.remove(enhanced_path)
                            print(f"Deleted enhanced image file: {enhanced_path}")

                return True
            else:
                print(f"Report file not found: {report_path}")
                return True  # Return True since the database entry was deleted

        except Exception as e:
            print(f"Error deleting report: {e}")
            return False

    def generate_report(self, diagnosis, image_path, confidence_score=None, recommendations=None):
        """Generate an enhanced professional PDF report with diagnosis and image"""
        try:
            from reportlab.lib.units import inch, cm
            from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
            from reportlab.graphics.shapes import Drawing, Line
            from reportlab.graphics.charts.piecharts import Pie
            from reportlab.graphics.charts.barcharts import VerticalBarChart
            from reportlab.graphics import renderPDF
            from reportlab.pdfgen import canvas
            import io
            import math

            # Create a custom PDF with more professional layout
            # Set up the document with margins
            margins = {
                'top': 1.0 * inch,
                'bottom': 1.0 * inch,
                'left': 1.0 * inch,
                'right': 1.0 * inch
            }

            doc = SimpleDocTemplate(
                self.filename,
                pagesize=letter,
                leftMargin=margins['left'],
                rightMargin=margins['right'],
                topMargin=margins['top'],
                bottomMargin=margins['bottom'],
                title=f"Scoliosis Assessment Report - {self.patient_name}"
            )

            # Register standard fonts to ensure they're available
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # Set up styles
            styles = getSampleStyleSheet()

            # Add custom styles
            styles.add(ParagraphStyle(
                name='CustomTitle',
                fontName='Helvetica-Bold',
                fontSize=20,
                alignment=TA_CENTER,
                spaceAfter=16,
                textColor=colors.HexColor('#0277bd')
            ))

            styles.add(ParagraphStyle(
                name='SubTitle',
                fontName='Helvetica-Bold',
                fontSize=14,
                alignment=TA_CENTER,
                spaceAfter=12,
                textColor=colors.HexColor('#0277bd')
            ))

            styles.add(ParagraphStyle(
                name='SectionHeader',
                fontName='Helvetica-Bold',
                fontSize=12,
                alignment=TA_LEFT,
                spaceBefore=12,
                spaceAfter=6,
                textColor=colors.HexColor('#0277bd'),
                borderWidth=0,
                borderColor=colors.HexColor('#0277bd'),
                borderPadding=5,
                backColor=colors.HexColor('#f5f9ff')
            ))

            styles.add(ParagraphStyle(
                name='NormalText',
                fontName='Helvetica',
                fontSize=10,
                alignment=TA_LEFT,
                spaceAfter=6,
                leading=14
            ))

            styles.add(ParagraphStyle(
                name='BulletPoint',
                fontName='Helvetica',
                fontSize=10,
                alignment=TA_LEFT,
                leftIndent=20,
                bulletIndent=10,
                spaceAfter=2,
                leading=14
            ))

            styles.add(ParagraphStyle(
                name='Footer',
                fontName='Helvetica',  # Changed from Helvetica-Italic to Helvetica
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.gray
            ))

            # Initialize elements list
            elements = []

            # Create a header directly as elements instead of using BytesIO
            # Add a styled title instead of an image header
            elements.append(Paragraph("SCOLIOSIS ASSESSMENT REPORT", styles['CustomTitle']))
            elements.append(Paragraph("Advanced Diagnostic Analysis", styles['SubTitle']))

            # Add a horizontal line
            def add_horizontal_line(color='#0277bd', thickness=2):
                d = Drawing(doc.width, thickness)
                d.add(Line(0, 0, doc.width, 0, strokeColor=colors.HexColor(color), strokeWidth=thickness))
                return d

            elements.append(add_horizontal_line())
            elements.append(Spacer(1, 0.3*inch))

            # Add report information in a table
            report_data = [
                ['Report ID:', f'SCL-{self.patient_id[-5:] if len(self.patient_id) >= 5 else self.patient_id}'],
                ['Date:', self.timestamp],
                ['Generated By:', 'Scoliosis Detection System v2.0']
            ]

            report_table = Table(report_data, colWidths=[1.5*inch, 4*inch])
            report_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#0277bd')),
            ]))

            elements.append(report_table)
            elements.append(Spacer(1, 0.3*inch))

            # Add a horizontal line
            def add_horizontal_line():
                d = Drawing(doc.width, 1)
                d.add(Line(0, 0, doc.width, 0, strokeColor=colors.HexColor('#0277bd'), strokeWidth=1))
                return d

            elements.append(add_horizontal_line())
            elements.append(Spacer(1, 0.2*inch))

            # Patient Information Section
            elements.append(Paragraph("Patient Information", styles['SectionHeader']))

            # Create a table for patient information
            patient_data = [
                ['Patient ID:', self.patient_id],
                ['Patient Name:', self.patient_name],
                ['First Visit:', 'Retrieving from database...'],  # In a real implementation, you would get this from the database
            ]

            patient_table = Table(patient_data, colWidths=[1.5*inch, 4*inch])
            patient_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f5f9ff')),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#d0e3f5')),
            ]))

            elements.append(patient_table)
            elements.append(Spacer(1, 0.2*inch))

            # Diagnosis Section
            elements.append(Paragraph("Diagnosis & Assessment", styles['SectionHeader']))

            # Process confidence score
            try:
                conf_score = float(confidence_score) if confidence_score is not None else 85.0
            except (ValueError, TypeError):
                conf_score = 85.0  # Default value if conversion fails

            # Determine if scoliosis is detected from the diagnosis text
            scoliosis_detected = "Scoliosis Detected" in diagnosis

            # Calculate Cobb angle based on image analysis
            # For now, we'll use a more realistic approach based on confidence and severity
            if scoliosis_detected:
                # Calculate Cobb angle based on confidence score
                # Higher confidence typically means more severe curvature
                # Range: 10-45 degrees for scoliosis cases
                base_angle = 10  # Minimum angle for scoliosis diagnosis
                max_angle = 45   # Maximum angle we'll report

                # Scale the angle based on confidence (higher confidence = more severe)
                confidence_factor = min(conf_score / 100, 1.0)  # Normalize to 0-1
                cobb_angle = round(base_angle + (max_angle - base_angle) * confidence_factor, 1)
            else:
                # For non-scoliosis cases, use a small angle (normal spine has slight curves)
                cobb_angle = round(random.uniform(1.0, 9.0), 1)  # Normal range: 1-9 degrees

            # Determine severity based on Cobb angle
            severity = "Mild"
            if cobb_angle > 25:
                severity = "Moderate"
            if cobb_angle > 40:
                severity = "Severe"

            # Determine curvature type and location (simulated for demonstration)
            # In a real implementation, this would come from your image analysis algorithm
            curve_type = "Right Thoracic"
            if scoliosis_detected:
                if cobb_angle > 30:
                    curve_type = "Double Major (Right Thoracic, Left Lumbar)"
                elif cobb_angle > 20:
                    curve_type = "Right Thoracolumbar"
                else:
                    curve_type = "Right Thoracic"
            else:
                curve_type = "Normal Alignment"

            # Determine curve location
            curve_location = "T5-T12" if "Thoracic" in curve_type else "T10-L3" if "Thoracolumbar" in curve_type else "T5-T12, L1-L5" if "Double" in curve_type else "N/A"

            # Create a table for diagnosis information
            diagnosis_data = [
                ['Diagnosis:', diagnosis],
                ['Confidence Score:', f"{conf_score:.1f}%"],
                ['Cobb Angle:', f"{cobb_angle}°"],
                ['Curve Type:', curve_type],
                ['Curve Location:', curve_location],
                ['Severity:', f"{severity} Scoliosis" if scoliosis_detected else "No significant curvature detected"],
            ]

            diagnosis_table = Table(diagnosis_data, colWidths=[1.5*inch, 4*inch])
            diagnosis_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f5f9ff')),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#d0e3f5')),
                # Highlight the severity row with a color based on the severity
                ('BACKGROUND', (1, 3), (1, 3),
                 colors.HexColor('#e6f7e6') if not scoliosis_detected or severity == "Mild" else
                 colors.HexColor('#fff8e1') if severity == "Moderate" else
                 colors.HexColor('#ffebee')),
                ('TEXTCOLOR', (1, 3), (1, 3),
                 colors.HexColor('#2e7d32') if not scoliosis_detected or severity == "Mild" else
                 colors.HexColor('#f57c00') if severity == "Moderate" else
                 colors.HexColor('#c62828')),
            ]))

            elements.append(diagnosis_table)
            elements.append(Spacer(1, 0.2*inch))

            # Add a visual representation of the Cobb angle with a dynamic spine visualization
            # Create a text-based visualization of the spine condition
            spine_visualization = []

            # Determine text and background colors based on severity
            if scoliosis_detected:
                severity_color = ('#e6f7e6' if severity == "Mild" else
                                '#fff8e1' if severity == "Moderate" else
                                '#ffebee')
                text_color = ('#2e7d32' if severity == "Mild" else
                            '#f57c00' if severity == "Moderate" else
                            '#c62828')

                # Determine curve pattern based on curve type
                curve_pattern = "S-shaped curve" if "Double" in curve_type else "C-shaped curve"

                # Create a dynamic ASCII visualization of the spine based on curve type and Cobb angle
                spine_ascii = self.generate_spine_ascii_art(cobb_angle, "Double" in curve_type)

                spine_visualization = [
                    [Paragraph(f"<b>Spine Condition:</b>", styles['NormalText']),
                     Paragraph(f"<font color='{text_color}'><b>{severity} Scoliosis</b></font>", styles['NormalText'])],
                    [Paragraph("<b>Cobb Angle:</b>", styles['NormalText']),
                     Paragraph(f"<font color='{text_color}'><b>{cobb_angle}°</b></font>", styles['NormalText'])],
                    [Paragraph("<b>Curve Type:</b>", styles['NormalText']),
                     Paragraph(f"{curve_type}", styles['NormalText'])],
                    [Paragraph("<b>Curve Pattern:</b>", styles['NormalText']),
                     Paragraph(f"{curve_pattern} in the spine", styles['NormalText'])],
                    [Paragraph("<b>Visualization:</b>", styles['NormalText']),
                     Paragraph(f"{spine_ascii}", styles['NormalText'])]
                ]
            else:
                # Create a straight spine ASCII visualization
                spine_ascii = self.generate_spine_ascii_art(0, False)

                spine_visualization = [
                    [Paragraph("<b>Spine Condition:</b>", styles['NormalText']),
                     Paragraph("<font color='#2e7d32'><b>Normal Alignment</b></font>", styles['NormalText'])],
                    [Paragraph("<b>Cobb Angle:</b>", styles['NormalText']),
                     Paragraph(f"<font color='#2e7d32'><b>{cobb_angle}°</b></font>", styles['NormalText'])],
                    [Paragraph("<b>Curve Type:</b>", styles['NormalText']),
                     Paragraph("Normal Alignment", styles['NormalText'])],
                    [Paragraph("<b>Curve Pattern:</b>", styles['NormalText']),
                     Paragraph("Straight spine alignment", styles['NormalText'])],
                    [Paragraph("<b>Visualization:</b>", styles['NormalText']),
                     Paragraph(f"{spine_ascii}", styles['NormalText'])]
                ]

            # Create a table for the spine visualization
            spine_table = Table(spine_visualization, colWidths=[1.5*inch, 4*inch])
            spine_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f5f9ff')),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#d0e3f5')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
            ]))

            elements.append(Paragraph("Spine Analysis", styles['SectionHeader']))
            elements.append(spine_table)
            elements.append(Spacer(1, 0.2*inch))

            # X-Ray Image Section
            elements.append(Paragraph("X-Ray Analysis", styles['SectionHeader']))

            # Add image if available
            if image_path and os.path.exists(image_path):
                try:
                    # Process the X-ray image to enhance clarity
                    try:
                        from PIL import Image as PILImage, ImageEnhance, ImageOps, ImageFilter

                        # Create enhanced version of the image
                        enhanced_image_path = f"reports/enhanced_{os.path.basename(image_path)}"

                        # Open and enhance the image
                        img = PILImage.open(image_path)

                        # Convert to grayscale if it's not already
                        if img.mode != 'L':
                            img = ImageOps.grayscale(img)

                        # Enhance contrast
                        enhancer = ImageEnhance.Contrast(img)
                        img = enhancer.enhance(1.8)  # Increase contrast by 80%

                        # Enhance brightness
                        enhancer = ImageEnhance.Brightness(img)
                        img = enhancer.enhance(1.3)  # Increase brightness by 30%

                        # Apply sharpening
                        img = img.filter(ImageFilter.SHARPEN)
                        img = img.filter(ImageFilter.SHARPEN)  # Apply twice for stronger effect

                        # Save the enhanced image
                        img.save(enhanced_image_path)

                        # Use the enhanced image
                        image_to_use = enhanced_image_path
                    except Exception as img_e:
                        print(f"Error enhancing image: {str(img_e)}")
                        image_to_use = image_path

                    # Create a frame for the image with a caption
                    image_data = [
                        [Image(image_to_use, width=5*inch, height=3.5*inch, kind='proportional')],
                        [f"Posterior-Anterior (PA) view showing {severity.lower() if scoliosis_detected else 'normal'} spine alignment"]
                    ]

                    # Create a background for the X-ray image
                    image_table = Table(image_data, colWidths=[5.5*inch])
                    image_table.setStyle(TableStyle([
                        ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                        ('ALIGN', (0, 1), (0, 1), 'CENTER'),
                        ('FONTNAME', (0, 1), (0, 1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (0, 1), 9),
                        ('TEXTCOLOR', (0, 1), (0, 1), colors.gray),
                        ('BOTTOMPADDING', (0, 0), (0, 0), 6),
                        ('TOPPADDING', (0, 1), (0, 1), 3),
                        ('BACKGROUND', (0, 0), (0, 0), colors.black),  # Black background for X-ray
                        ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                    ]))

                    elements.append(image_table)
                except Exception as e:
                    elements.append(Paragraph(f"<i>Error including image: {str(e)}</i>", styles['NormalText']))
            else:
                elements.append(Paragraph("<i>No X-ray image available</i>", styles['NormalText']))

            elements.append(Spacer(1, 0.2*inch))

            # Recommendations Section
            if recommendations:
                elements.append(Paragraph("Recommendations", styles['SectionHeader']))

                # Create a table for recommendations with bullet points
                rec_data = []
                for i, rec in enumerate(recommendations):
                    rec_data.append([f"{i+1}.", rec])

                if rec_data:
                    rec_table = Table(rec_data, colWidths=[0.3*inch, 5.2*inch])
                    rec_table.setStyle(TableStyle([
                        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                        ('TOPPADDING', (0, 0), (-1, -1), 6),
                        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                        ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f5f9ff')),
                        ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#d0e3f5')),
                    ]))

                    elements.append(rec_table)

            elements.append(Spacer(1, 0.3*inch))

            # Add a follow-up section
            elements.append(Paragraph("Follow-up Plan", styles['SectionHeader']))

            # Create follow-up recommendations based on severity
            follow_up_text = "Regular check-up in 12 months is recommended."
            if scoliosis_detected:
                if severity == "Mild":
                    follow_up_text = "Follow-up examination in 6 months is recommended to monitor progression."
                elif severity == "Moderate":
                    follow_up_text = "Follow-up examination in 3 months is recommended. Consider consultation with an orthopedic specialist."
                else:  # Severe
                    follow_up_text = "Immediate consultation with an orthopedic specialist is recommended. Follow-up within 4-6 weeks."

            elements.append(Paragraph(follow_up_text, styles['NormalText']))
            elements.append(Spacer(1, 0.3*inch))

            # Add a disclaimer
            elements.append(Paragraph("Disclaimer", styles['SectionHeader']))
            disclaimer_text = (
                "This report is generated by an automated system and should be reviewed by a qualified healthcare "
                "professional. The analysis is based on image processing algorithms and may not capture all clinical "
                "aspects of the patient's condition. This report is not a substitute for professional medical advice, "
                "diagnosis, or treatment."
            )
            elements.append(Paragraph(disclaimer_text, styles['NormalText']))

            # Add a footer with page numbers
            def add_page_number(canvas, doc):
                canvas.saveState()
                canvas.setFont('Helvetica', 8)  # Using standard Helvetica font
                canvas.setFillColor(colors.grey)

                # Add footer text
                footer_text = f"Scoliosis Assessment Report | Patient: {self.patient_name} | ID: {self.patient_id}"
                canvas.drawCentredString(doc.width/2 + doc.leftMargin, 0.5*inch, footer_text)

                # Add page number
                page_num = canvas.getPageNumber()
                canvas.drawRightString(doc.width + doc.leftMargin - 10, 0.5*inch, f"Page {page_num}")

                # Add timestamp
                canvas.drawString(doc.leftMargin, 0.5*inch, self.timestamp)

                # Add a line above the footer
                canvas.setStrokeColor(colors.lightgrey)
                canvas.line(doc.leftMargin, 0.75*inch, doc.width + doc.leftMargin, 0.75*inch)

                canvas.restoreState()

            # Build PDF with the footer function
            doc.build(elements, onFirstPage=add_page_number, onLaterPages=add_page_number)

            return self.filename
        except Exception as e:
            print(f"Error generating report: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def save_to_database(self, diagnosis, image_path):
        """Save report information to database"""
        try:
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None
            print(f"Saving report with user_id: {user_id}")

            # Insert report data with user_id
            try:
                cursor.execute('''INSERT INTO reports
                                 (patient_id, patient_name, timestamp, diagnosis, image_path, report_path, user_id)
                                 VALUES (?, ?, ?, ?, ?, ?, ?)''',
                               (self.patient_id, self.patient_name, self.timestamp,
                                diagnosis, image_path, self.filename, user_id))

                print(f"Saved report for patient {self.patient_id} with user {user_id}")
            except sqlite3.OperationalError as e:
                # If the user_id column doesn't exist, try without it
                print(f"Error saving report with user_id: {e}")

                # Check if reports table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
                table_exists = cursor.fetchone() is not None

                if not table_exists:
                    # Create table if it doesn't exist (with user_id column)
                    cursor.execute('''CREATE TABLE IF NOT EXISTS reports
                                     (patient_id TEXT, patient_name TEXT, timestamp TEXT,
                                      diagnosis TEXT, image_path TEXT, report_path TEXT, user_id TEXT)''')
                    print("Created reports table with user_id column")

                    # Try again with user_id
                    cursor.execute('''INSERT INTO reports
                                     (patient_id, patient_name, timestamp, diagnosis, image_path, report_path, user_id)
                                     VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                   (self.patient_id, self.patient_name, self.timestamp,
                                    diagnosis, image_path, self.filename, user_id))
                else:
                    # Table exists but might not have user_id column, try without it
                    cursor.execute('''INSERT INTO reports
                                     (patient_id, patient_name, timestamp, diagnosis, image_path, report_path)
                                     VALUES (?, ?, ?, ?, ?, ?)''',
                                   (self.patient_id, self.patient_name, self.timestamp,
                                    diagnosis, image_path, self.filename))

                    print(f"Saved report for patient {self.patient_id} without user_id")

            # Update patient's last visit time
            try:
                # First try with 'id' column (which is the correct column name in the new schema)
                cursor.execute('''UPDATE patients SET last_visit = ? WHERE id = ?''',
                              (self.timestamp, self.patient_id))
            except sqlite3.OperationalError:
                # If that fails, try with 'patient_id' column (which might be used in older schema)
                try:
                    cursor.execute('''UPDATE patients SET last_visit = ? WHERE patient_id = ?''',
                                  (self.timestamp, self.patient_id))
                except sqlite3.OperationalError:
                    # If both fail, just print an error message
                    print(f"Error updating patient last visit time: Could not find appropriate column name")

            # Also update the user_id for this patient if it's not set
            if user_id:
                try:
                    cursor.execute('''UPDATE patients SET user_id = ?
                                     WHERE id = ? AND (user_id IS NULL OR user_id = '')''',
                                  (user_id, self.patient_id))
                    if cursor.rowcount > 0:
                        print(f"Updated user_id for patient {self.patient_id} to {user_id}")
                except sqlite3.OperationalError as e:
                    print(f"Error updating patient user_id: {e}")

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error saving to database: {str(e)}")
            raise

    def email_report(self, recipient_email, subject=None, message=None):
        """Email the generated report"""
        if not os.path.exists(self.filename):
            return False, "Report file not found"

        try:
            # Import the email service
            from email_service import email_service

            # Create default subject and message if not provided
            if not subject:
                subject = f"Scoliosis Detection Report - {self.patient_name}"
            if not message:
                message = f"Please find attached the scoliosis detection report for {self.patient_name}."

            # Show sending status dialog
            status_dialog = tk.Toplevel()
            status_dialog.title("Sending Email")
            status_dialog.geometry("300x100")

            # Center the dialog
            status_dialog.update_idletasks()
            width = status_dialog.winfo_width()
            height = status_dialog.winfo_height()
            x = (status_dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (status_dialog.winfo_screenheight() // 2) - (height // 2)
            status_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Status message
            status_label = tk.Label(status_dialog, text="Sending email...", font=("Arial", 12))
            status_label.pack(pady=20)

            # Update the UI
            status_dialog.update()

            # Send the email
            success, message = email_service.send_email(
                recipient_email=recipient_email,
                subject=subject,
                message=message,
                attachment_path=self.filename
            )

            # Close the status dialog
            status_dialog.destroy()

            # Show result message
            if success:
                messagebox.showinfo("Success", "Email sent successfully!")
            else:
                messagebox.showerror("Error", message)

            return success, message
        except Exception as e:
            print(f"Error sending email: {str(e)}")
            return False, f"Failed to send email: {str(e)}"

    def generate_html_report(self, diagnosis, image_path, confidence_score=None, recommendations=None):
        """Generate an enhanced professional HTML report with diagnosis and image"""
        try:
            # Create reports directory if it doesn't exist
            os.makedirs("reports", exist_ok=True)

            # Set filename for HTML report with timestamp to ensure uniqueness
            html_filename = f"reports/{self.patient_id}_{self.patient_name.replace(' ', '_')}_{self.timestamp_short}.html"

            # Convert confidence score to float if possible
            try:
                conf_score = float(confidence_score) if confidence_score is not None else 85.0
            except (ValueError, TypeError):
                conf_score = 85.0  # Default value if conversion fails

            # Calculate Cobb angle based on image analysis
            # For now, we'll use a more realistic approach based on confidence and severity
            scoliosis_detected = "Scoliosis Detected" in diagnosis
            if scoliosis_detected:
                # Calculate Cobb angle based on confidence score
                # Higher confidence typically means more severe curvature
                # Range: 10-45 degrees for scoliosis cases
                base_angle = 10  # Minimum angle for scoliosis diagnosis
                max_angle = 45   # Maximum angle we'll report

                # Scale the angle based on confidence (higher confidence = more severe)
                confidence_factor = min(conf_score / 100, 1.0)  # Normalize to 0-1
                cobb_angle = round(base_angle + (max_angle - base_angle) * confidence_factor, 1)
            else:
                # For non-scoliosis cases, use a small angle (normal spine has slight curves)
                cobb_angle = round(random.uniform(1.0, 9.0), 1)  # Normal range: 1-9 degrees

            # Determine severity based on Cobb angle
            severity = "Mild"
            if cobb_angle > 25:
                severity = "Moderate"
            if cobb_angle > 40:
                severity = "Severe"

            # Determine curvature type and location (simulated for demonstration)
            # In a real implementation, this would come from your image analysis algorithm
            scoliosis_detected = "Scoliosis Detected" in diagnosis
            curve_type = "Right Thoracic"
            if scoliosis_detected:
                if cobb_angle > 30:
                    curve_type = "Double Major (Right Thoracic, Left Lumbar)"
                elif cobb_angle > 20:
                    curve_type = "Right Thoracolumbar"
                else:
                    curve_type = "Right Thoracic"
            else:
                curve_type = "Normal Alignment"

            # Determine curve location
            curve_location = "T5-T12" if "Thoracic" in curve_type else "T10-L3" if "Thoracolumbar" in curve_type else "T5-T12, L1-L5" if "Double" in curve_type else "N/A"

            # Create HTML content with enhanced professional design
            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Scoliosis Assessment Report - {self.patient_name}</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

                    :root {{
                        --primary-color: #0277bd;
                        --secondary-color: #58a5f0;
                        --accent-color: #004c8c;
                        --text-color: #333;
                        --light-bg: #f5f9ff;
                        --warning-color: #f57c00;
                        --success-color: #2e7d32;
                        --border-radius: 8px;
                    }}

                    * {{
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                    }}

                    body {{
                        font-family: 'Roboto', Arial, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        max-width: 1000px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #fff;
                    }}

                    .report-container {{
                        border: 1px solid #ddd;
                        border-radius: var(--border-radius);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        overflow: hidden;
                    }}

                    .header {{
                        background-color: var(--primary-color);
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }}

                    .header h1 {{
                        font-size: 28px;
                        margin-bottom: 5px;
                    }}

                    .header p {{
                        font-size: 14px;
                        opacity: 0.9;
                    }}

                    .report-body {{
                        padding: 30px;
                    }}

                    .section {{
                        margin-bottom: 30px;
                    }}

                    .section-title {{
                        color: var(--primary-color);
                        border-bottom: 2px solid var(--secondary-color);
                        padding-bottom: 8px;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                    }}

                    .section-title i {{
                        margin-right: 10px;
                        font-size: 20px;
                    }}

                    .patient-info {{
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        display: flex;
                        flex-wrap: wrap;
                    }}

                    .patient-info-col {{
                        flex: 1;
                        min-width: 250px;
                    }}

                    .info-item {{
                        margin-bottom: 10px;
                    }}

                    .info-item strong {{
                        display: inline-block;
                        width: 120px;
                        color: var(--accent-color);
                    }}

                    .diagnosis-box {{
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--primary-color);
                    }}

                    .diagnosis-summary {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 15px;
                    }}

                    .diagnosis-text {{
                        flex: 2;
                    }}

                    .confidence-meter {{
                        flex: 1;
                        text-align: right;
                    }}

                    .confidence-value {{
                        font-size: 24px;
                        font-weight: bold;
                        color: var(--primary-color);
                    }}

                    .severity-indicator {{
                        display: inline-block;
                        padding: 5px 10px;
                        border-radius: 15px;
                        font-weight: bold;
                        margin-top: 10px;
                        background-color: #81c784;
                        color: white;
                    }}

                    .severity-moderate {{
                        background-color: #ffb74d;
                    }}

                    .severity-severe {{
                        background-color: #e57373;
                    }}

                    .cobb-angle {{
                        display: flex;
                        align-items: center;
                        margin-top: 15px;
                    }}

                    .cobb-angle-value {{
                        font-size: 22px;
                        font-weight: bold;
                        color: var(--accent-color);
                        margin-right: 10px;
                    }}

                    .spine-visualization {{
                        display: flex;
                        justify-content: center;
                        margin: 20px 0;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: var(--border-radius);
                    }}

                    .spine {{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        position: relative;
                        height: 300px;
                        width: 100px;
                    }}

                    .vertebra {{
                        width: 20px;
                        height: 20px;
                        background-color: var(--primary-color);
                        border-radius: 50%;
                        margin: 5px 0;
                        position: absolute;
                    }}

                    .spine-label {{
                        position: absolute;
                        left: -30px;
                        font-size: 10px;
                        color: var(--accent-color);
                    }}

                    .angle-marker {{
                        position: absolute;
                        color: red;
                        font-weight: bold;
                        font-size: 14px;
                    }}

                    .image-section {{
                        margin-top: 20px;
                        text-align: center;
                    }}

                    .image-container {{
                        background-color: #000; /* Dark background for X-ray contrast */
                        padding: 20px;
                        border-radius: var(--border-radius);
                        margin: 0 auto;
                        max-width: 90%;
                    }}

                    .image-container img {{
                        width: 100%;
                        max-height: 700px; /* Increased height for better visibility */
                        object-fit: contain;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                    }}

                    /* Lightbox effect for image */
                    .image-container img:hover {{
                        cursor: pointer;
                        opacity: 0.9;
                    }}

                    .image-caption {{
                        margin-top: 15px;
                        font-size: 14px;
                        color: #fff;
                        text-align: center;
                        font-style: italic;
                    }}

                    .recommendations {{
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--warning-color);
                    }}

                    .recommendations ul {{
                        padding-left: 20px;
                    }}

                    .recommendations li {{
                        margin-bottom: 10px;
                        position: relative;
                        padding-left: 5px;
                    }}

                    .recommendations li::before {{
                        content: "•";
                        color: var(--warning-color);
                        font-weight: bold;
                        display: inline-block;
                        width: 1em;
                        margin-left: -1em;
                    }}

                    .download-section {{
                        margin-top: 30px;
                        text-align: center;
                        padding: 20px;
                        background-color: var(--light-bg);
                        border-radius: var(--border-radius);
                    }}

                    .btn {{
                        display: inline-block;
                        padding: 10px 20px;
                        margin: 10px;
                        border-radius: 4px;
                        text-decoration: none;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background-color 0.3s;
                        border: none;
                    }}

                    .btn-primary {{
                        background-color: var(--primary-color);
                        color: white;
                    }}

                    .btn-primary:hover {{
                        background-color: var(--accent-color);
                    }}

                    .btn-secondary {{
                        background-color: var(--secondary-color);
                        color: white;
                    }}

                    .btn-secondary:hover {{
                        background-color: #4294d0;
                    }}

                    .footer {{
                        margin-top: 30px;
                        text-align: center;
                        font-size: 0.8em;
                        color: #777;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }}

                    /* Modal for lightbox */
                    .modal {{
                        display: none;
                        position: fixed;
                        z-index: 1000;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        overflow: auto;
                        background-color: rgba(0,0,0,0.9);
                    }}

                    .modal-content {{
                        margin: auto;
                        display: block;
                        max-width: 90%;
                        max-height: 90%;
                    }}

                    .modal-close {{
                        position: absolute;
                        top: 15px;
                        right: 35px;
                        color: #f1f1f1;
                        font-size: 40px;
                        font-weight: bold;
                        transition: 0.3s;
                        cursor: pointer;
                    }}

                    .modal-close:hover {{
                        color: #bbb;
                    }}

                    /* Icons */
                    .icon {{
                        display: inline-block;
                        width: 24px;
                        height: 24px;
                        margin-right: 10px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                    }}

                    .icon-patient {{
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
                    }}

                    .icon-diagnosis {{
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
                    }}

                    .icon-image {{
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>');
                    }}

                    .icon-recommendations {{
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/></svg>');
                    }}

                    /* Spine visualization styles */
                    .spine-visualization {{
                        position: relative;
                        height: 350px;
                        border: 1px solid #ddd;
                        border-radius: var(--border-radius);
                        background-color: var(--light-bg);
                        margin-top: 15px;
                        overflow: hidden;
                        padding-top: 10px;
                    }}

                    .spine {{
                        position: relative;
                        width: 100%;
                        height: 100%;
                    }}

                    .curve-info {{
                        position: absolute;
                        top: 10px;
                        left: 50%;
                        transform: translateX(-50%);
                        text-align: center;
                        font-size: 14px;
                        padding: 5px 10px;
                        border-radius: 5px;
                        background-color: rgba(255,255,255,0.8);
                        z-index: 10;
                    }}

                    .vertebra {{
                        position: absolute;
                        width: 20px;
                        height: 10px;
                        background-color: var(--primary-color);
                        border-radius: 2px;
                        transition: all 0.3s ease;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                    }}

                    .spine-label {{
                        position: absolute;
                        left: 10px;
                        font-size: 10px;
                        color: var(--accent-color);
                        font-weight: bold;
                        text-shadow: 0 0 2px white;
                    }}

                    .angle-marker {{
                        position: absolute;
                        padding: 5px 10px;
                        background-color: rgba(255,255,255,0.9);
                        border-radius: 15px;
                        font-weight: bold;
                        color: var(--primary-color);
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        transition: all 0.3s ease;
                    }}
                </style>
                <script>
                    // Add JavaScript for image modal functionality
                    function openModal() {{
                        document.getElementById('imageModal').style.display = "block";
                        document.getElementById('modalImg').src = document.getElementById('xrayImage').src;
                    }}

                    function closeModal() {{
                        document.getElementById('imageModal').style.display = "none";
                    }}

                    // Function to create spine visualization
                    function createSpineVisualization() {{
                        const spineContainer = document.getElementById('spine-container');
                        if (!spineContainer) return;

                        // Clear any existing visualization
                        spineContainer.innerHTML = '';

                        // Get spine parameters from data attributes
                        const isDouble = {str("Double" in curve_type).lower()};
                        const isScoliosis = {str(scoliosis_detected).lower()};
                        const cobbAngle = {cobb_angle};
                        const curveType = "{curve_type}";
                        const severity = "{severity}";

                        // Create spine container with data attributes for future updates
                        const spine = document.createElement('div');
                        spine.className = 'spine';
                        spine.dataset.cobbAngle = cobbAngle;
                        spine.dataset.isDouble = isDouble;
                        spine.dataset.isScoliosis = isScoliosis;
                        spine.dataset.curveType = curveType;
                        spine.dataset.severity = severity;
                        spineContainer.appendChild(spine);

                        // Create vertebrae
                        const vertebraeCount = 12;
                        const labels = ['T1', 'T3', 'T5', 'T7', 'T9', 'T11', 'L1', 'L3', 'L5'];

                        // Add a title with the curve information
                        const curveInfo = document.createElement('div');
                        curveInfo.className = 'curve-info';
                        if (isScoliosis) {{
                            curveInfo.innerHTML = `<strong>${{severity}} Scoliosis</strong><br>Cobb Angle: ${{cobbAngle}}°<br>${{curveType}}`;
                            curveInfo.style.color = severity === 'Mild' ? '#2e7d32' : severity === 'Moderate' ? '#f57c00' : '#c62828';
                        }} else {{
                            curveInfo.innerHTML = '<strong>Normal Alignment</strong><br>No significant curvature';
                            curveInfo.style.color = '#2e7d32';
                        }}
                        spine.appendChild(curveInfo);

                        for (let i = 0; i < vertebraeCount; i++) {{
                            const vertebra = document.createElement('div');
                            vertebra.className = 'vertebra';

                            const height = 300;
                            const yPos = (i / (vertebraeCount - 1)) * (height - 20);

                            let xOffset = 0;
                            if (isScoliosis) {{
                                if (isDouble) {{
                                    // Create S-curve for double curve
                                    const phase = (i / (vertebraeCount - 1)) * Math.PI * 2;
                                    xOffset = Math.sin(phase) * (cobbAngle / 2);
                                }} else {{
                                    // Create C-curve for single curve
                                    const normalizedPos = (i / (vertebraeCount - 1)) * 2 - 1; // -1 to 1
                                    xOffset = Math.pow(normalizedPos, 2) * (cobbAngle / 2) * (normalizedPos < 0 ? -1 : 1);
                                }}
                            }}

                            vertebra.style.top = `${{yPos}}px`;
                            vertebra.style.left = `${{50 + xOffset}}px`;

                            // Color the vertebrae based on severity
                            if (isScoliosis) {{
                                vertebra.style.backgroundColor = severity === 'Mild' ? '#81c784' :
                                                               severity === 'Moderate' ? '#ffb74d' : '#e57373';
                            }}

                            spine.appendChild(vertebra);

                            // Add labels for some vertebrae
                            if (i % 2 === 0 || i === vertebraeCount - 1) {{
                                const label = document.createElement('div');
                                label.className = 'spine-label';
                                label.textContent = labels[Math.floor(i / 2)];
                                label.style.top = `${{yPos}}px`;
                                spine.appendChild(label);
                            }}
                        }}

                        // Add Cobb angle marker
                        if (isScoliosis) {{
                            const angleMarker = document.createElement('div');
                            angleMarker.className = 'angle-marker';
                            angleMarker.textContent = `${{cobbAngle}}°`;
                            angleMarker.style.top = '150px';
                            angleMarker.style.right = '10px';

                            // Color the angle marker based on severity
                            angleMarker.style.backgroundColor = severity === 'Mild' ? '#e8f5e9' :
                                                              severity === 'Moderate' ? '#fff8e1' : '#ffebee';
                            angleMarker.style.color = severity === 'Mild' ? '#2e7d32' :
                                                    severity === 'Moderate' ? '#f57c00' : '#c62828';
                            angleMarker.style.border = `2px solid ${{severity === 'Mild' ? '#81c784' :
                                                                  severity === 'Moderate' ? '#ffb74d' : '#e57373'}}`;

                            spine.appendChild(angleMarker);
                        }}
                    }}

                    // Initialize spine visualization when page loads
                    window.onload = function() {{
                        createSpineVisualization();
                    }};
                </script>
            </head>
            <body>
                <div class="report-container">
                    <div class="header">
                        <h1>Scoliosis Assessment Report</h1>
                        <p>Generated on {self.timestamp}</p>
                    </div>

                    <div class="report-body">
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-patient"></span>Patient Information</h2>
                            <div class="patient-info">
                                <div class="patient-info-col">
                                    <div class="info-item">
                                        <strong>Patient ID:</strong> {self.patient_id}
                                    </div>
                                    <div class="info-item">
                                        <strong>Patient Name:</strong> {self.patient_name}
                                    </div>
                                </div>
                                <div class="patient-info-col">
                                    <div class="info-item">
                                        <strong>Date:</strong> {self.timestamp}
                                    </div>
                                    <div class="info-item">
                                        <strong>Report ID:</strong> SCL-{self.patient_id[-5:] if len(self.patient_id) >= 5 else self.patient_id}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-diagnosis"></span>Diagnosis</h2>
                            <div class="diagnosis-box">
                                <div class="diagnosis-summary">
                                    <div class="diagnosis-text">
                                        <p>{diagnosis}</p>
                                        <div class="severity-indicator {'severity-moderate' if severity == 'Moderate' else 'severity-severe' if severity == 'Severe' else ''}">
                                            {severity} Scoliosis
                                        </div>
                                    </div>
                                    <div class="confidence-meter">
                                        <div>Confidence</div>
                                        <div class="confidence-value">{conf_score:.1f}%</div>
                                    </div>
                                </div>

                                <div class="cobb-angle">
                                    <div class="cobb-angle-value">{cobb_angle}°</div>
                                    <div>Cobb Angle Measurement</div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-diagnosis"></span>Curvature Analysis</h2>
                            <div class="diagnosis-box">
                                <div style="display: flex; flex-wrap: wrap;">
                                    <div style="flex: 1; min-width: 300px;">
                                        <div class="info-item">
                                            <strong>Curve Type:</strong> {curve_type}
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Location:</strong> {curve_location}
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Direction:</strong> {"Right" if "Right" in curve_type else "Left" if "Left" in curve_type else "Right and Left" if "Double" in curve_type else "None"}
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Pattern:</strong> {"S-shaped" if "Double" in curve_type else "C-shaped" if scoliosis_detected else "Normal"}
                                        </div>
                                    </div>
                                    <div style="flex: 1; min-width: 300px;">
                                        <div class="spine-visualization">
                                            <div id="spine-container"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-image"></span>X-Ray Analysis</h2>
            """

            # Add image if available
            if image_path and os.path.exists(image_path):
                try:
                    # Process the X-ray image to enhance clarity
                    try:
                        from PIL import Image as PILImage, ImageEnhance, ImageOps, ImageFilter

                        # Create enhanced version of the image
                        enhanced_image_path = f"reports/enhanced_{os.path.basename(image_path)}"

                        # Open and enhance the image
                        img = PILImage.open(image_path)

                        # Convert to grayscale if it's not already
                        if img.mode != 'L':
                            img = ImageOps.grayscale(img)

                        # Enhance contrast
                        enhancer = ImageEnhance.Contrast(img)
                        img = enhancer.enhance(1.8)  # Increase contrast by 80%

                        # Enhance brightness
                        enhancer = ImageEnhance.Brightness(img)
                        img = enhancer.enhance(1.3)  # Increase brightness by 30%

                        # Apply sharpening
                        img = img.filter(ImageFilter.SHARPEN)
                        img = img.filter(ImageFilter.SHARPEN)  # Apply twice for stronger effect

                        # Save the enhanced image
                        img.save(enhanced_image_path)

                        # Use the enhanced image
                        image_filename = enhanced_image_path
                    except Exception as img_e:
                        print(f"Error enhancing image for HTML: {str(img_e)}")
                        # Copy original image to reports directory
                        image_filename = f"reports/img_{self.patient_id}_{os.path.basename(image_path)}"
                        shutil.copy2(image_path, image_filename)

                    # Add image to HTML with lightbox functionality and enhanced display
                    html_content += f"""
                            <div class="image-section">
                                <div class="image-container">
                                    <img id="xrayImage" src="{os.path.basename(image_filename)}" alt="Patient X-Ray" onclick="openModal()">
                                    <div class="image-caption">Posterior-Anterior (PA) view showing {severity.lower()} scoliotic curve with Cobb angle of {cobb_angle}°</div>
                                </div>
                            </div>

                            <!-- Modal for full-size image -->
                            <div id="imageModal" class="modal">
                                <span class="modal-close" onclick="closeModal()">&times;</span>
                                <img class="modal-content" id="modalImg">
                            </div>
                    """
                except Exception as e:
                    print(f"Error copying image: {str(e)}")
                    # Add error message to HTML instead of failing
                    html_content += f"""
                            <div class="image-section">
                                <div class="image-container">
                                    <p style="color: red; padding: 40px;">Error including image: {str(e)}</p>
                                </div>
                            </div>
                    """

            # Add recommendations
            if recommendations:
                html_content += """
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Recommendations</h2>
                            <div class="recommendations">
                                <ul>
                """

                for rec in recommendations:
                    html_content += f"                <li>{rec}</li>\n"

                html_content += """
                                </ul>
                            </div>
                        </div>
                """

            # Add follow-up plan
            follow_up_text = "Regular check-up in 12 months is recommended."
            if scoliosis_detected:
                if severity == "Mild":
                    follow_up_text = "Follow-up examination in 6 months is recommended to monitor progression."
                elif severity == "Moderate":
                    follow_up_text = "Follow-up examination in 3 months is recommended. Consider consultation with an orthopedic specialist."
                else:  # Severe
                    follow_up_text = "Immediate consultation with an orthopedic specialist is recommended. Follow-up within 4-6 weeks."

            html_content += f"""
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Follow-up Plan</h2>
                            <div class="recommendations" style="border-left: 4px solid var(--primary-color);">
                                <p>{follow_up_text}</p>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Disclaimer</h2>
                            <div class="recommendations" style="border-left: 4px solid #999;">
                                <p style="font-size: 0.9em; color: #666;">
                                    This report is generated by an automated system and should be reviewed by a qualified healthcare
                                    professional. The analysis is based on image processing algorithms and may not capture all clinical
                                    aspects of the patient's condition. This report is not a substitute for professional medical advice,
                                    diagnosis, or treatment.
                                </p>
                            </div>
                        </div>
            """

            # Add download button for PDF
            html_content += f"""
                        <div class="download-section">
                            <h3>Download Options</h3>
                            <a href="{os.path.basename(self.filename)}" class="btn btn-primary" download>Download as PDF</a>
                            <button onclick="window.print()" class="btn btn-secondary">Print Report</button>
                        </div>
                    </div>

                    <div class="footer">
                        <p>This report was generated by the Scoliosis Detection System.</p>
                        <p>© {datetime.now().year} Scoliosis Detection System | Confidential Medical Record</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # Write HTML to file
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return html_filename
        except Exception as e:
            print(f"Error generating HTML report: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

