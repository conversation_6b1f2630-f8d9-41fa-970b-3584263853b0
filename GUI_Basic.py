import tkinter as tk
from tkinter import ttk, filedialog
from PIL import Image, ImageTk, ImageEnhance
import os
import tkinter.simpledialog as sd
import tkinter.messagebox as mb
import random
import sys
import subprocess
import json
import datetime
import webbrowser
import threading
import time
import sqlite3
from patient_database import get_db_instance
from user_database import set_current_user, get_current_user
from auto_refresh import trigger_refresh

# Import theme manager
from theme_manager import theme_manager
from theme_toggle import ThemeToggle

# Advanced features are disabled in this version
ADVANCED_FEATURES_AVAILABLE = False

# Placeholder functions for advanced features (to avoid errors)
def analyze_xray(image_path):
    """Placeholder for the analyze_xray function"""
    return {"primary_curve": None, "output_files": {}, "image_path": image_path}

def track_progression(patient_id):
    """Placeholder for the track_progression function"""
    return {"progression_data": None, "output_files": {}}

class ProgressionTracker:
    """Placeholder for the ProgressionTracker class"""
    def __init__(self):
        self.database_path = "patient_data.db"

    def add_patient(self, name, dob, gender):
        """Placeholder for add_patient method"""
        return f"P{random.randint(1000, 9999)}"

    def add_examination(self, patient_id, date, image_path, data, notes=None):
        """Placeholder for add_examination method"""
        return random.randint(1, 1000)

def reconstruct_spine_3d(vertebrae_data):
    """Placeholder for the reconstruct_spine_3d function"""
    return {"output_files": {}}

# Global variables
fn = ""
current_image_path = ""
current_report_path = ""
current_html_report_path = ""
prediction_confidence = 95  # Default value
current_user = {"name": "Guest", "role": "user"}  # Default user
current_vertebrae_data = None  # For storing AI Cobb angle analysis results
diagnosis_text = ""  # For storing the diagnosis text from analysis
current_patient_id = ""  # For storing the current patient ID
current_patient_name = ""  # For storing the current patient name

# Try to load the current user from command line arguments
if len(sys.argv) > 1:
    username = sys.argv[1]
    # Load user data from users.json if it exists
    if os.path.exists("users.json"):
        try:
            with open("users.json", "r") as f:
                users = json.load(f)
                if username in users:
                    current_user = {
                        "name": users[username]["name"],
                        "role": users[username]["role"],
                        "username": username
                    }

                    # Set the current user in the user_database module
                    set_current_user(username)
        except Exception as e:
            print(f"Error loading user data: {e}")

# Get colors from theme manager
PRIMARY_COLOR = theme_manager.get_color("primary_color")
SECONDARY_COLOR = theme_manager.get_color("secondary_color")
ACCENT_COLOR = theme_manager.get_color("accent_color")
BG_COLOR = theme_manager.get_color("bg_color")
TEXT_COLOR = theme_manager.get_color("text_color")
HIGHLIGHT_COLOR = theme_manager.get_color("highlight_color")

# Initialize the main window
root = tk.Tk()
root.configure(background=BG_COLOR)

# Get screen dimensions
w, h = root.winfo_screenwidth(), root.winfo_screenheight()
root.geometry("%dx%d+0+0" % (w, h))
root.title("Scoliosis Detection System")

# Apply theme to ttk widgets
theme_manager.apply_theme_to_ttk(root)

# Function to update the header gradient
def update_header_gradient():
    header_canvas.delete("all")  # Clear the canvas

    # Create gradient effect
    for i in range(80):
        # Gradient from PRIMARY_COLOR to a slightly lighter shade
        r = int(int(PRIMARY_COLOR[1:3], 16) + (i/80) * 20)
        g = int(int(PRIMARY_COLOR[3:5], 16) + (i/80) * 20)
        b = int(int(PRIMARY_COLOR[5:7], 16) + (i/80) * 20)
        color = f'#{r:02x}{g:02x}{b:02x}'
        header_canvas.create_line(0, i, w, i, fill=color)

    # Update header elements
    title_label.config(bg=PRIMARY_COLOR, fg='white')
    subtitle_label.config(bg=PRIMARY_COLOR, fg='white')
    user_frame.config(bg=PRIMARY_COLOR)
    user_icon.config(bg=PRIMARY_COLOR)
    user_icon.delete("all")
    user_icon.create_oval(5, 5, 25, 25, fill=SECONDARY_COLOR, outline="")
    user_info.config(bg=PRIMARY_COLOR)
    user_name_label.config(bg=PRIMARY_COLOR, fg='white')
    user_role_label.config(bg=PRIMARY_COLOR, fg='white')

# Function to update all widgets with the current theme
def update_all_widgets():
    # Update main frames
    for widget in [main_frame, control_panel, content_area,
                  button_frame, analysis_tab, history_tab, reports_tab]:
        if widget.winfo_exists():
            theme_manager.apply_theme_to_widgets([widget])

    # Update all buttons
    for widget in root.winfo_children():
        if isinstance(widget, tk.Button) or isinstance(widget, ttk.Button):
            if isinstance(widget, tk.Button):
                widget.config(
                    bg=theme_manager.get_color("button_bg"),
                    fg=theme_manager.get_color("button_fg"),
                    activebackground=theme_manager.get_color("highlight_color")
                )

    # Update all labels
    for widget in root.winfo_children():
        if isinstance(widget, tk.Label) or isinstance(widget, ttk.Label):
            if isinstance(widget, tk.Label):
                widget.config(
                    bg=theme_manager.get_color("label_bg"),
                    fg=theme_manager.get_color("label_fg")
                )

    # Force redraw
    root.update_idletasks()

# Function to update the UI when theme changes
def update_theme():
    # Update color variables
    global PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BG_COLOR, TEXT_COLOR, HIGHLIGHT_COLOR
    PRIMARY_COLOR = theme_manager.get_color("primary_color")
    SECONDARY_COLOR = theme_manager.get_color("secondary_color")
    ACCENT_COLOR = theme_manager.get_color("accent_color")
    BG_COLOR = theme_manager.get_color("bg_color")
    TEXT_COLOR = theme_manager.get_color("text_color")
    HIGHLIGHT_COLOR = theme_manager.get_color("highlight_color")

    # Update root background
    root.configure(background=BG_COLOR)

    # Apply theme to ttk widgets
    theme_manager.apply_theme_to_ttk(root)

    # Update header canvas
    update_header_gradient()

    # Update all frames and widgets
    update_all_widgets()

# Register the update function with the theme manager
theme_manager.register_callback(update_theme)

# Create a modern header
header_frame = ttk.Frame(root, style='TFrame', height=80)
header_frame.pack(fill='x', side='top')

# Add a gradient background to the header
header_canvas = tk.Canvas(header_frame, height=80, bg=PRIMARY_COLOR, highlightthickness=0)
header_canvas.pack(fill='x')

# Create gradient effect
for i in range(80):
    # Gradient from PRIMARY_COLOR to a slightly lighter shade
    r = int(44 + (i/80) * 20)  # 2c -> 44 in decimal
    g = int(62 + (i/80) * 20)  # 3e -> 62 in decimal
    b = int(80 + (i/80) * 20)  # 50 -> 80 in decimal
    color = f'#{r:02x}{g:02x}{b:02x}'
    header_canvas.create_line(0, i, w, i, fill=color)

# Add logo and title to the header
try:
    # Try to load the logo
    logo_img = Image.open('logo.png')
    logo_img = logo_img.resize((60, 60))
    logo_photo = ImageTk.PhotoImage(logo_img)
    logo_label = tk.Label(header_canvas, image=logo_photo, bg=PRIMARY_COLOR)
    logo_label.image = logo_photo
    logo_label.place(x=20, y=10)
    title_x = 100
except Exception as e:
    print(f"Could not load logo: {e}")
    # If no logo is available, adjust title position
    title_x = 20

# Add title
title_label = tk.Label(header_canvas, text="Scoliosis Detection System",
                      font=('Segoe UI', 24, 'bold'), bg=PRIMARY_COLOR, fg='white')
title_label.place(x=title_x, y=20)

# Add a subtitle
subtitle_label = tk.Label(header_canvas, text="Advanced X-ray Analysis & Reporting",
                         font=('Segoe UI', 12), bg=PRIMARY_COLOR, fg='white')
subtitle_label.place(x=title_x, y=55)

# Add theme toggle button
theme_toggle_frame = tk.Frame(header_canvas, bg=PRIMARY_COLOR)
theme_toggle_frame.place(relx=0.85, y=20, anchor='ne')

# Create theme toggle
theme_toggle = ThemeToggle(theme_toggle_frame, callback=update_theme, bg=PRIMARY_COLOR)
theme_toggle.pack(side='left', padx=10)

# Add user profile info
user_frame = tk.Frame(header_canvas, bg=PRIMARY_COLOR)
user_frame.place(relx=0.95, y=20, anchor='ne')

# User icon (a simple circle)
user_icon = tk.Canvas(user_frame, width=30, height=30, bg=PRIMARY_COLOR, highlightthickness=0)
user_icon.create_oval(5, 5, 25, 25, fill=SECONDARY_COLOR, outline="")
user_icon.pack(side='left', padx=(0, 10))

# User name and role
user_info = tk.Frame(user_frame, bg=PRIMARY_COLOR)
user_info.pack(side='left')

user_name_label = tk.Label(user_info, text=current_user["name"],
                         font=('Segoe UI', 12, 'bold'), bg=PRIMARY_COLOR, fg='white')
user_name_label.pack(anchor='e')

user_role_label = tk.Label(user_info, text=current_user["role"].capitalize(),
                         font=('Segoe UI', 8), bg=PRIMARY_COLOR, fg='white')
user_role_label.pack(anchor='e')

# Create a main content frame with two columns
main_frame = ttk.Frame(root, style='TFrame')
main_frame.pack(fill='both', expand=True, padx=20, pady=20)

# Left column - Control panel
control_panel = ttk.Frame(main_frame, style='TFrame', width=280)
control_panel.pack(side='left', fill='y', padx=(0, 20))
# Make sure the control panel maintains its width
control_panel.pack_propagate(False)

# Add a title to the control panel
control_title = ttk.Label(control_panel, text="Analysis Controls", style='Subheader.TLabel')
control_title.pack(pady=(0, 20), anchor='w', padx=10)

# Add a separator above the workflow buttons
ttk.Separator(control_panel, orient='horizontal').pack(fill='x', pady=5)

# Add workflow steps label
workflow_label = ttk.Label(control_panel, text="Workflow Steps", font=('Segoe UI', 12, 'bold'))
workflow_label.pack(anchor='w', pady=(10, 15), padx=10)

# Create a frame for the buttons with a light background
button_frame = ttk.Frame(control_panel, style='TFrame')
button_frame.pack(fill='x', padx=10)

# Right column - Content area
content_area = ttk.Frame(main_frame, style='TFrame')
content_area.pack(side='left', fill='both', expand=True)

# Create tabs for different views
tab_control = ttk.Notebook(content_area)
tab_control.pack(fill='both', expand=True)

# Image Analysis Tab
analysis_tab = ttk.Frame(tab_control, style='TFrame')
tab_control.add(analysis_tab, text='Image Analysis')

# Create a frame for the image display
image_display_frame = ttk.Frame(analysis_tab, style='TFrame')
image_display_frame.pack(fill='both', expand=True, padx=10, pady=10)

# Create a frame for the images with a grid layout
images_grid = ttk.Frame(image_display_frame, style='TFrame')
images_grid.pack(fill='both', expand=True)

# Configure grid weights
images_grid.columnconfigure(0, weight=1)
images_grid.columnconfigure(1, weight=1)
images_grid.columnconfigure(2, weight=1)
images_grid.rowconfigure(0, weight=1)

# Create a frame for the original image
original_frame = ttk.LabelFrame(images_grid, text="Original X-ray", style='TFrame')
original_frame.grid(row=0, column=0, padx=10, pady=10, sticky='nsew')

# Create a frame for the grayscale image
gray_frame = ttk.LabelFrame(images_grid, text="Grayscale Image", style='TFrame')
gray_frame.grid(row=0, column=1, padx=10, pady=10, sticky='nsew')

# Create a frame for the binary/threshold image
binary_frame = ttk.LabelFrame(images_grid, text="Binary Image", style='TFrame')
binary_frame.grid(row=0, column=2, padx=10, pady=10, sticky='nsew')

# Add placeholders for the images
image_placeholder = ttk.Label(original_frame, text="Select an image to begin analysis", font=('Segoe UI', 14))
image_placeholder.pack(pady=50)

gray_placeholder = ttk.Label(gray_frame, text="Click 'Image Preprocessing' to view", font=('Segoe UI', 10))
gray_placeholder.pack(pady=50)

binary_placeholder = ttk.Label(binary_frame, text="Click 'Image Preprocessing' to view", font=('Segoe UI', 10))
binary_placeholder.pack(pady=50)

# Create a frame for the results
results_frame = ttk.LabelFrame(analysis_tab, text="Analysis Results", style='TFrame')
results_frame.pack(fill='x', padx=10, pady=10, ipady=10)

# Create a label for the results
results_label = ttk.Label(results_frame, text="No analysis performed yet", font=('Segoe UI', 12))
results_label.pack(pady=10)

# Patient History Tab
history_tab = ttk.Frame(tab_control, style='TFrame')
tab_control.add(history_tab, text='Patient History')

# Create patient history content
history_frame = ttk.Frame(history_tab, style='TFrame')
history_frame.pack(fill='both', expand=True, padx=20, pady=20)

# Add a header
history_header = ttk.Label(history_frame, text="Patient History", style='Subheader.TLabel')
history_header.pack(anchor='w', pady=(0, 20))

# Add description
history_desc = ttk.Label(history_frame, text="View and manage patient examination history, including past diagnoses and reports.")
history_desc.pack(anchor='w', pady=(0, 20))

# Add patient search frame
patient_search_frame = ttk.Frame(history_frame, style='TFrame')
patient_search_frame.pack(fill='x', pady=10)

ttk.Label(patient_search_frame, text="Search by patient name:").pack(side='left', padx=5)

patient_search_var = tk.StringVar()
patient_search_entry = ttk.Entry(patient_search_frame, textvariable=patient_search_var, width=30)
patient_search_entry.pack(side='left', padx=5)

def search_patients():
    """Search for patients and display results"""
    search_term = patient_search_var.get().strip()
    if not search_term:
        mb.showinfo("Search", "Please enter a patient name to search.")
        return

    # Clear existing items
    for item in patient_tree.get_children():
        patient_tree.delete(item)

    # This would normally query the database
    # For demo purposes, we'll just add some dummy data
    import random
    for i in range(5):
        patient_id = f"P{random.randint(1000, 9999)}"
        patient_name = f"{search_term} {chr(65+i)}"
        last_visit = "2023-05-10 14:30:00"

        patient_tree.insert('', 'end', values=(
            patient_id,
            patient_name,
            format_date(last_visit)
        ))

def format_date(date_str):
    """Format a date string for display"""
    try:
        import datetime
        dt = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        return dt.strftime("%b %d, %Y %I:%M %p")
    except:
        return date_str

search_btn = ttk.Button(patient_search_frame, text="Search", command=search_patients)
search_btn.pack(side='left', padx=5)

# Add patient list
patient_list_frame = ttk.Frame(history_frame, style='TFrame')
patient_list_frame.pack(fill='both', expand=True, pady=10)

# Global variables for patient history
patient_tree = None
patient_search_var = None

# Add button to view patient history
def view_patient_history():
    """View the selected patient's history"""
    selected_items = patient_tree.selection()
    if not selected_items:
        mb.showinfo("Selection Required", "Please select a patient to view.")
        return

    # Get the selected patient's ID and name
    item = selected_items[0]
    values = patient_tree.item(item, 'values')

    # Make sure we're getting the correct values
    # The treeview columns are ('id', 'name', 'last_visit')
    patient_id = values[0]  # First column is ID
    patient_name = values[1]  # Second column is Name

    print(f"Selected patient: ID={patient_id}, Name={patient_name}")

    # Display the patient history in the current window
    display_patient_history(patient_id, patient_name)

def delete_selected_patient():
    """Delete the selected patient and all their examinations"""
    selected_items = patient_tree.selection()
    if not selected_items:
        mb.showinfo("Selection Required", "Please select a patient to delete.")
        return

    # Get the selected patient's ID and name
    item = selected_items[0]
    values = patient_tree.item(item, 'values')

    # Make sure we're getting the correct values
    # The treeview columns are ('id', 'name', 'last_visit')
    patient_id = values[0]  # First column is ID
    patient_name = values[1]  # Second column is Name

    # Confirm deletion
    if not mb.askyesno("Confirm Deletion",
                      f"Are you sure you want to delete patient {patient_name} (ID: {patient_id}) and all their examinations?\n\nThis action cannot be undone."):
        return

    # Get database instance
    db = get_db_instance()

    # Delete the patient
    if db.delete_patient(patient_id):
        mb.showinfo("Success", f"Patient {patient_name} and all their examinations have been deleted.")
        # Refresh the patient list
        refresh_patient_list()
    else:
        mb.showerror("Error", f"Failed to delete patient {patient_name}. Please try again.")

def display_patient_history(patient_id, patient_name):
    """Display the patient history in the current window"""
    # Clear the history frame
    for widget in history_frame.winfo_children():
        widget.destroy()

    # Create a header with back button
    header_frame = ttk.Frame(history_frame)
    header_frame.pack(fill='x', padx=10, pady=10)

    # Back button
    back_btn = ttk.Button(
        header_frame,
        text="← Back to Patient List",
        command=lambda: refresh_history_tab()
    )
    back_btn.pack(side='left', padx=5, pady=5)

    # Refresh button for patient history
    refresh_btn = ttk.Button(
        header_frame,
        text="↻ Refresh",
        command=lambda: trigger_refresh(root, lambda: refresh_patient_detail(patient_id, patient_name)),
        width=8
    )
    refresh_btn.pack(side='right', padx=10, pady=5)

    # Patient info
    patient_info_frame = ttk.Frame(header_frame)
    patient_info_frame.pack(side='left', padx=20)

    patient_name_label = ttk.Label(
        patient_info_frame,
        text=patient_name,
        font=('Segoe UI', 16, 'bold')
    )
    patient_name_label.pack(anchor='w')

    patient_id_label = ttk.Label(
        patient_info_frame,
        text=f"Patient ID: {patient_id}"
    )
    patient_id_label.pack(anchor='w')

    # Create a frame for the content
    content_frame = ttk.Frame(history_frame)
    content_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Create a PanedWindow
    paned_window = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
    paned_window.pack(fill='both', expand=True)

    # Left pane - Examination list
    exam_frame = ttk.Frame(paned_window)
    paned_window.add(exam_frame, weight=1)

    # Examination list header
    ttk.Label(
        exam_frame,
        text="Examination History",
        font=('Segoe UI', 12, 'bold')
    ).pack(anchor='w', padx=5, pady=5)

    # Create a treeview for examinations
    exam_tree = ttk.Treeview(
        exam_frame,
        columns=('date', 'diagnosis'),
        show='headings'
    )
    exam_tree.heading('date', text='Date')
    exam_tree.heading('diagnosis', text='Diagnosis')
    exam_tree.column('date', width=150)
    exam_tree.column('diagnosis', width=200)
    exam_tree.pack(fill='both', expand=True, padx=5, pady=5)

    # Add scrollbar to treeview
    exam_scrollbar = ttk.Scrollbar(exam_tree, orient="vertical", command=exam_tree.yview)
    exam_scrollbar.pack(side='right', fill='y')
    exam_tree.configure(yscrollcommand=exam_scrollbar.set)

    # Right pane - Examination details
    detail_frame = ttk.Frame(paned_window)
    paned_window.add(detail_frame, weight=2)

    # Examination details header
    ttk.Label(
        detail_frame,
        text="Examination Details",
        font=('Segoe UI', 12, 'bold')
    ).pack(anchor='w', padx=5, pady=5)

    # Create a frame for examination details
    detail_content = ttk.Frame(detail_frame)
    detail_content.pack(fill='both', expand=True, padx=5, pady=5)

    # Placeholder for when no examination is selected
    placeholder_label = ttk.Label(
        detail_content,
        text="Select an examination to view details"
    )
    placeholder_label.pack(pady=50)

    # Function to handle examination selection
    def on_exam_selected(event):
        """Handle selection of an examination"""
        selected_items = exam_tree.selection()
        if not selected_items:
            return

        # Get the selected examination's ID
        item = selected_items[0]
        exam_id = int(exam_tree.item(item, 'tags')[0])

        # Load examination details
        load_examination_details(exam_id, detail_content)

    # Bind selection event
    exam_tree.bind('<<TreeviewSelect>>', on_exam_selected)

    # Load examinations for the patient
    load_patient_examinations(patient_id, exam_tree)

def refresh_patient_detail(patient_id, patient_name):
    """Refresh the patient detail view without going back to the list"""
    # Show a brief message
    update_status("Refreshing patient history...")

    # Redisplay the patient history with fresh data
    display_patient_history(patient_id, patient_name)

    # Update status
    update_status(f"Patient history for {patient_name} refreshed successfully.")

def load_patient_examinations(patient_id, exam_tree):
    """Load examinations for the patient into the treeview"""
    # Get database instance
    db = get_db_instance()

    # Clear existing items
    for item in exam_tree.get_children():
        exam_tree.delete(item)

    # Get examinations from the database
    examinations = db.get_patient_examinations(patient_id)

    if not examinations:
        # Insert a placeholder item if no examinations found
        exam_tree.insert('', 'end', values=("No examinations found", ""), tags=("0",))
        return

    # Add examinations to the treeview
    for exam in examinations:
        exam_tree.insert(
            '', 'end',
            values=(
                format_date(exam['exam_date']),
                exam['diagnosis']
            ),
            tags=(str(exam['id']),)
        )

def load_examination_details(exam_id, detail_content):
    """Load and display examination details"""
    # Get database instance
    db = get_db_instance()

    # Get examination from the database
    exam = db.get_examination(exam_id)
    if not exam:
        mb.showerror("Error", f"Examination with ID {exam_id} not found.")
        return

    # Clear the detail content frame
    for widget in detail_content.winfo_children():
        widget.destroy()

    # Create a canvas with scrollbar for the details
    canvas = tk.Canvas(detail_content, bg=BG_COLOR, highlightthickness=0)
    scrollbar = ttk.Scrollbar(detail_content, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Add examination details to the scrollable frame
    # Date and time
    date_frame = ttk.Frame(scrollable_frame)
    date_frame.pack(fill='x', padx=10, pady=5)

    ttk.Label(date_frame, text="Date:", font=('Segoe UI', 10, 'bold')).pack(side='left')
    ttk.Label(date_frame, text=format_date(exam['exam_date'])).pack(side='left', padx=5)

    # Diagnosis
    diag_frame = ttk.Frame(scrollable_frame)
    diag_frame.pack(fill='x', padx=10, pady=5)

    ttk.Label(diag_frame, text="Diagnosis:", font=('Segoe UI', 10, 'bold')).pack(side='left')
    ttk.Label(diag_frame, text=exam['diagnosis']).pack(side='left', padx=5)

    # Confidence score
    if exam['confidence']:
        conf_frame = ttk.Frame(scrollable_frame)
        conf_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(conf_frame, text="Confidence Score:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(conf_frame, text=f"{exam['confidence']:.2f}%").pack(side='left', padx=5)

    # Add separator
    ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=10, pady=10)

    # X-ray image
    if exam['image_path'] and os.path.exists(exam['image_path']):
        img_frame = ttk.Frame(scrollable_frame)
        img_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(img_frame, text="X-ray Image:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

        try:
            # Load and resize the image
            img = Image.open(exam['image_path'])
            img.thumbnail((400, 400))  # Resize to fit
            photo = ImageTk.PhotoImage(img)

            # Display the image
            img_label = ttk.Label(img_frame, image=photo)
            img_label.image = photo  # Keep a reference
            img_label.pack(pady=5)

            # Add a button to view the full image
            view_btn = ttk.Button(
                img_frame,
                text="View Full Image",
                command=lambda: view_full_image(exam['image_path'])
            )
            view_btn.pack(pady=5)
        except Exception as e:
            ttk.Label(img_frame, text=f"Error loading image: {str(e)}").pack(pady=5)

    # Report links
    if exam['report_path'] or exam['html_report_path']:
        report_frame = ttk.Frame(scrollable_frame)
        report_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(report_frame, text="Reports:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

        if exam['report_path'] and os.path.exists(exam['report_path']):
            pdf_btn = ttk.Button(
                report_frame,
                text="View PDF Report",
                command=lambda: open_file(exam['report_path'])
            )
            pdf_btn.pack(anchor='w', pady=2)

        if exam['html_report_path'] and os.path.exists(exam['html_report_path']):
            html_btn = ttk.Button(
                report_frame,
                text="View HTML Report",
                command=lambda: open_file(exam['html_report_path'])
            )
            html_btn.pack(anchor='w', pady=2)

    # Notes section
    notes_frame = ttk.Frame(scrollable_frame)
    notes_frame.pack(fill='x', padx=10, pady=10)

    ttk.Label(notes_frame, text="Notes:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

    # Create a text widget for notes with a save button
    notes_text = tk.Text(notes_frame, height=5, width=40, wrap='word')
    notes_text.pack(fill='x', pady=5)

    if exam['notes']:
        notes_text.insert('1.0', exam['notes'])

    # Save notes button
    save_btn = ttk.Button(
        notes_frame,
        text="Save Notes",
        command=lambda: save_examination_notes(exam_id, notes_text)
    )
    save_btn.pack(anchor='e', pady=5)

def save_examination_notes(exam_id, notes_text):
    """Save the notes for an examination"""
    # Get database instance
    db = get_db_instance()

    notes = notes_text.get('1.0', 'end-1c')  # Get text without the final newline

    if db.update_examination_notes(exam_id, notes):
        mb.showinfo("Success", "Notes saved successfully.")
    else:
        mb.showerror("Error", "Failed to save notes.")

def view_full_image(image_path):
    """Open the image in a new window at full size"""
    if not os.path.exists(image_path):
        mb.showerror("Error", "Image file not found.")
        return

    # Create a new top-level window
    img_window = tk.Toplevel(root)
    img_window.title("X-ray Image")

    try:
        # Load the image
        img = Image.open(image_path)

        # Get screen dimensions
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()

        # Calculate maximum size while maintaining aspect ratio
        max_width = screen_width * 0.8
        max_height = screen_height * 0.8

        width, height = img.size
        scale = min(max_width / width, max_height / height)

        new_width = int(width * scale)
        new_height = int(height * scale)

        # Resize the image
        img = img.resize((new_width, new_height), Image.LANCZOS)
        photo = ImageTk.PhotoImage(img)

        # Display the image
        img_label = ttk.Label(img_window, image=photo)
        img_label.image = photo  # Keep a reference
        img_label.pack(padx=10, pady=10)

        # Add a close button
        close_btn = ttk.Button(img_window, text="Close", command=img_window.destroy)
        close_btn.pack(pady=10)

        # Center the window
        img_window.update_idletasks()
        x = (screen_width - img_window.winfo_width()) // 2
        y = (screen_height - img_window.winfo_height()) // 2
        img_window.geometry(f"+{x}+{y}")

    except Exception as e:
        mb.showerror("Error", f"Error displaying image: {str(e)}")
        img_window.destroy()

def open_file(file_path):
    """Open a file with the default application"""
    if not os.path.exists(file_path):
        mb.showerror("Error", f"File not found: {file_path}")
        return

    try:
        # Get the absolute path to ensure it works correctly
        abs_path = os.path.abspath(file_path)
        print(f"Opening file: {abs_path}")

        # Open file with default application based on platform
        if sys.platform == 'win32':
            os.startfile(abs_path)
        elif sys.platform == 'darwin':  # macOS
            subprocess.call(['open', abs_path])
        else:  # Linux
            subprocess.call(['xdg-open', abs_path])
    except Exception as e:
        mb.showerror("Error", f"Error opening file: {str(e)}")
        print(f"Error details: {e}")

def search_patients():
    """Search for patients by name"""
    global patient_search_var, patient_tree

    if patient_search_var is None or patient_tree is None:
        mb.showerror("Error", "Patient search not initialized properly.")
        return

    search_term = patient_search_var.get().strip().lower()

    # Get database instance
    db = get_db_instance()

    # Get all patients from the database
    patients = db.get_all_patients()

    # Clear existing items
    for item in patient_tree.get_children():
        patient_tree.delete(item)

    # Filter patients by name if search term is provided
    if search_term:
        filtered_patients = [p for p in patients if search_term in p['name'].lower()]
    else:
        filtered_patients = patients

    # Add filtered patients to the treeview
    for patient in filtered_patients:
        patient_tree.insert('', 'end', values=(
            patient['id'],
            patient['name'],
            format_date(patient['last_visit']) if patient['last_visit'] else "No visits"
        ))

def format_date(date_str):
    """Format a date string for display"""
    if not date_str:
        return "N/A"

    try:
        # Parse the date string
        date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        # Format it in a more readable way
        return date_obj.strftime("%b %d, %Y %I:%M %p")
    except Exception:
        return date_str



def refresh_history_tab():
    """Refresh the patient history tab to show the patient list"""
    global patient_tree, patient_search_var

    # Force a direct refresh of the database connection to ensure we get fresh data
    # This is critical for ensuring we see the latest data
    import patient_database
    patient_database._instance = None  # Reset the singleton instance

    # Now get a fresh database instance
    db = get_db_instance()
    print("Forced database refresh in refresh_history_tab")

    # Clear the history frame
    for widget in history_frame.winfo_children():
        widget.destroy()

    # Recreate the patient history content
    # Add a header
    history_header = ttk.Label(history_frame, text="Patient History", font=('Segoe UI', 16, 'bold'))
    history_header.pack(anchor='w', pady=(10, 10))

    # Add description
    history_desc = ttk.Label(history_frame, text="View and manage patient examination history, including past diagnoses and reports.")
    history_desc.pack(anchor='w', pady=(0, 10))

    # Add patient search frame at the top
    patient_search_frame = ttk.Frame(history_frame)
    patient_search_frame.pack(fill='x', pady=10)

    ttk.Label(patient_search_frame, text="Search by patient name:").pack(side='left', padx=5)

    patient_search_var = tk.StringVar()
    patient_search_entry = ttk.Entry(patient_search_frame, textvariable=patient_search_var, width=30)
    patient_search_entry.pack(side='left', padx=5)

    search_btn = ttk.Button(patient_search_frame, text="Search", command=search_patients)
    search_btn.pack(side='left', padx=5)

    # Add refresh button
    refresh_btn = ttk.Button(
        patient_search_frame,
        text="↻ Refresh Patient List",
        command=lambda: refresh_patient_list(),
        width=18
    )
    refresh_btn.pack(side='right', padx=5)

    # Add patient list frame
    patient_list_frame = ttk.Frame(history_frame)
    patient_list_frame.pack(fill='both', expand=True, pady=10)

    # Create a treeview for patients with examination count
    patient_tree = ttk.Treeview(patient_list_frame, columns=('id', 'name', 'exams', 'last_visit'), show='headings', height=15)
    patient_tree.heading('id', text='Patient ID')
    patient_tree.heading('name', text='Patient Name')
    patient_tree.heading('exams', text='Exams')
    patient_tree.heading('last_visit', text='Last Visit')

    # Configure columns with better widths and alignment
    patient_tree.column('id', width=100, minwidth=80, anchor='center')
    patient_tree.column('name', width=180, minwidth=150, anchor='w')
    patient_tree.column('exams', width=60, minwidth=50, anchor='center')
    patient_tree.column('last_visit', width=160, minwidth=140, anchor='center')

    # Add alternating row colors for better visibility
    patient_tree.tag_configure('oddrow', background='#f0f0f0')
    patient_tree.tag_configure('evenrow', background='white')

    # Add scrollbar to the patient tree
    scrollbar_y = ttk.Scrollbar(patient_list_frame, orient="vertical", command=patient_tree.yview)
    patient_tree.configure(yscrollcommand=scrollbar_y.set)

    # Pack the treeview and scrollbar
    patient_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
    scrollbar_y.pack(side='right', fill='y', pady=5)

    # Add scrollbar to treeview
    patient_scrollbar = ttk.Scrollbar(patient_list_frame, orient="vertical", command=patient_tree.yview)
    patient_scrollbar.pack(side='right', fill='y')
    patient_tree.configure(yscrollcommand=patient_scrollbar.set)

    # Create a button frame for the patient history tab
    history_button_frame = ttk.Frame(history_frame)
    history_button_frame.pack(fill='x', pady=10)

    # Add button to view patient history
    view_history_btn = ttk.Button(history_button_frame, text="View Selected Patient History", command=view_patient_history)
    view_history_btn.pack(side='left', padx=5)

    # Add button to delete patient
    delete_patient_btn = ttk.Button(history_button_frame, text="Delete Patient", command=delete_selected_patient)
    delete_patient_btn.pack(side='left', padx=5)

    # Load patient data automatically
    refresh_patient_list()
    print("Patient list refreshed in refresh_history_tab")

def refresh_patient_list():
    """Refresh the patient list without recreating the entire tab"""
    global patient_tree

    if patient_tree is None:
        mb.showerror("Error", "Patient tree not initialized properly.")
        return

    # Show a brief message
    update_status("Refreshing patient history...")

    # Clear the search field
    if patient_search_var is not None:
        patient_search_var.set("")

    # Force a direct refresh of the database connection to ensure we get fresh data
    # This is critical for ensuring we see the latest data
    import patient_database
    patient_database._instance = None  # Reset the singleton instance

    # Now get a fresh database instance
    db = get_db_instance()

    # Import user_database functions
    from user_database import get_current_user_id, is_authenticated

    # Get current user ID
    user_id = get_current_user_id() if is_authenticated() else None

    if not user_id:
        print("No user authenticated, not refreshing patient list")
        update_status("Error: No user authenticated")
        return

    # Get all patients from the database for the current user
    # The get_all_patients method now filters by the current user
    patients = db.get_all_patients()
    print(f"Found {len(patients)} patients for user {user_id}")

    # Clear existing items
    for item in patient_tree.get_children():
        patient_tree.delete(item)

    # Add patients to the treeview with alternating row colors
    for index, patient in enumerate(patients):
        # Handle last_visit formatting
        last_visit = "No visits"
        if 'last_visit' in patient and patient['last_visit']:
            try:
                last_visit = format_date(patient['last_visit'])
            except Exception as e:
                print(f"Error formatting date for patient {patient['id']}: {e}")
                last_visit = str(patient['last_visit'])

        # Get examination count
        exam_count = patient.get('examination_count', 0)

        # Determine row color
        row_tag = 'evenrow' if index % 2 == 0 else 'oddrow'

        patient_tree.insert('', 'end', values=(
            patient['id'],
            patient['name'],
            exam_count,
            last_visit
        ), tags=(row_tag,))

    # Update status
    update_status("Patient history refreshed successfully.")
    print(f"Patient list refreshed with {len(patients)} patients for user {user_id}")

    # Load some sample data if needed (only if no real patients exist)
    if not patients:
        print("No real patients found, loading sample data")
        load_sample_patients()
    else:
        print(f"Found {len(patients)} real patients, not loading sample data")

def load_sample_patients():
    """Load sample patients into the treeview"""
    global patient_tree

    if patient_tree is None:
        mb.showerror("Error", "Patient tree not initialized properly.")
        return

    # Get database instance
    db = get_db_instance()

    # Import user_database functions
    from user_database import get_current_user_id, is_authenticated

    # Get current user ID
    user_id = get_current_user_id() if is_authenticated() else None

    if not user_id:
        print("No user authenticated, not adding sample data")
        return

    # Get all patients from the database for the current user
    patients = db.get_all_patients()

    # If no patients in the database for this user, add some sample data
    if not patients:
        print(f"No existing patients found for user {user_id}, adding sample data")
        try:
            print(f"Adding sample patients for user: {user_id}")

            # Generate unique patient IDs based on user ID to avoid conflicts
            prefix = user_id[:2].upper() if len(user_id) >= 2 else "P"

            # Add sample patients with user-specific IDs
            patient_id1 = f"{prefix}1001"
            patient_id2 = f"{prefix}1002"
            patient_id3 = f"{prefix}1003"

            # Add sample patients with explicit user_id
            db.add_patient(patient_id1, "John Smith")
            db.add_patient(patient_id2, "Jane Doe")
            db.add_patient(patient_id3, "Robert Johnson")

            # Add sample examinations
            db.add_examination(
                patient_id=patient_id1,
                diagnosis="Scoliosis Detected",
                confidence=95.5,
                notes="Initial examination"
            )

            db.add_examination(
                patient_id=patient_id2,
                diagnosis="No Scoliosis Detected",
                confidence=98.2,
                notes="Regular check-up"
            )

            print(f"Added sample patients and examinations for user {user_id}")

            # Get patients again
            patients = db.get_all_patients()
            print(f"After adding sample data, found {len(patients)} patients")
        except Exception as e:
            mb.showerror("Database Error", f"Error adding sample data: {str(e)}")
            return
    else:
        print(f"User {user_id} already has {len(patients)} patients, not adding sample data")

    # Clear existing items
    for item in patient_tree.get_children():
        patient_tree.delete(item)

    # Add patients to the treeview with alternating row colors
    for index, patient in enumerate(patients):
        # Handle last_visit formatting
        last_visit = "No visits"
        if 'last_visit' in patient and patient['last_visit']:
            try:
                last_visit = format_date(patient['last_visit'])
            except Exception as e:
                print(f"Error formatting date for sample patient {patient['id']}: {e}")
                last_visit = str(patient['last_visit'])

        # Get examination count
        exam_count = patient.get('examination_count', 0)

        # Determine row color
        row_tag = 'evenrow' if index % 2 == 0 else 'oddrow'

        patient_tree.insert('', 'end', values=(
            patient['id'],
            patient['name'],
            exam_count,
            last_visit
        ), tags=(row_tag,))

# Reports Tab
reports_tab = ttk.Frame(tab_control, style='TFrame')
tab_control.add(reports_tab, text='Reports')

# Advanced Features Tab (only if available)
if ADVANCED_FEATURES_AVAILABLE:
    advanced_tab = ttk.Frame(tab_control, style='TFrame')
    tab_control.add(advanced_tab, text='Advanced Analysis')

    # Create advanced features content
    advanced_frame = ttk.Frame(advanced_tab, style='TFrame')
    advanced_frame.pack(fill='both', expand=True, padx=20, pady=20)

    # Add a header
    advanced_header = ttk.Label(advanced_frame, text="Advanced Scoliosis Analysis", style='Subheader.TLabel')
    advanced_header.pack(anchor='w', pady=(0, 10))

    # Add description
    advanced_desc = ttk.Label(advanced_frame,
                            text="Use AI-powered tools to analyze X-rays, track progression, and create 3D spine models.")
    advanced_desc.pack(anchor='w', pady=(0, 20))

    # Create a notebook for the different advanced features
    advanced_notebook = ttk.Notebook(advanced_frame)
    advanced_notebook.pack(fill='both', expand=True)

    # 1. AI Cobb Angle Tab
    cobb_tab = ttk.Frame(advanced_notebook, style='TFrame')
    advanced_notebook.add(cobb_tab, text='AI Cobb Angle')

    # Create content for Cobb angle tab
    cobb_frame = ttk.Frame(cobb_tab, style='TFrame')
    cobb_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Add instructions
    ttk.Label(cobb_frame, text="Automatically detect vertebrae and calculate Cobb angles using AI",
            font=('Segoe UI', 12, 'bold')).pack(anchor='w', pady=(0, 10))

    ttk.Label(cobb_frame, text="Select an X-ray image and click 'Analyze' to detect vertebrae and calculate Cobb angles automatically.").pack(anchor='w', pady=(0, 20))

    # Add image selection and analysis buttons
    cobb_buttons_frame = ttk.Frame(cobb_frame, style='TFrame')
    cobb_buttons_frame.pack(fill='x', pady=10)

    def select_xray_for_cobb():
        """Select an X-ray image for Cobb angle analysis"""
        file_path = filedialog.askopenfilename(
            title="Select X-ray Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.tif *.tiff")]
        )
        if file_path:
            cobb_image_path_var.set(file_path)
            # Display the selected image
            try:
                img = Image.open(file_path)
                img.thumbnail((400, 400))
                photo = ImageTk.PhotoImage(img)
                cobb_image_label.config(image=photo)
                cobb_image_label.image = photo
                update_status(f"X-ray image selected: {os.path.basename(file_path)}")
            except Exception as e:
                mb.showerror("Error", f"Error loading image: {str(e)}")

    def analyze_cobb_angle():
        """Analyze the X-ray image to detect vertebrae and calculate Cobb angles"""
        image_path = cobb_image_path_var.get()
        if not image_path or not os.path.exists(image_path):
            mb.showerror("Error", "Please select a valid X-ray image first.")
            return

        # Show a progress message
        update_status("Analyzing X-ray image... This may take a moment.")
        cobb_result_label.config(text="Analysis in progress...")

        # Run the analysis in a separate thread to keep the UI responsive
        def run_analysis():
            try:
                # Call the analyze_xray function from cobb_angle_ai.py
                results = analyze_xray(image_path)

                # Update the UI with the results
                root.after(0, lambda: display_cobb_results(results))
            except Exception as e:
                root.after(0, lambda: mb.showerror("Error", f"Error analyzing X-ray: {str(e)}"))
                root.after(0, lambda: update_status("Analysis failed."))

        threading.Thread(target=run_analysis).start()

    def display_cobb_results(results):
        """Display the Cobb angle analysis results"""
        # Update status
        update_status("Analysis complete!")

        # Display the primary curve information
        if results["primary_curve"]:
            primary = results["primary_curve"]
            result_text = f"Primary Curve: {primary['upper_vertebra']}-{primary['lower_vertebra']} ({primary['angle']:.1f}°)"
            cobb_result_label.config(text=result_text)
        else:
            cobb_result_label.config(text="No significant curves detected.")

        # Display the vertebrae visualization
        try:
            img = Image.open(results["output_files"]["vertebrae_visualization"])
            img.thumbnail((400, 400))
            photo = ImageTk.PhotoImage(img)
            cobb_vertebrae_label.config(image=photo)
            cobb_vertebrae_label.image = photo
        except Exception as e:
            print(f"Error displaying vertebrae visualization: {e}")

        # Display the Cobb angle visualization
        try:
            img = Image.open(results["output_files"]["cobb_angle_visualization"])
            img.thumbnail((400, 400))
            photo = ImageTk.PhotoImage(img)
            cobb_angles_label.config(image=photo)
            cobb_angles_label.image = photo
        except Exception as e:
            print(f"Error displaying Cobb angle visualization: {e}")

        # Store the results for use with other features
        global current_vertebrae_data
        current_vertebrae_data = results

    # Create a variable to store the selected image path
    cobb_image_path_var = tk.StringVar()

    # Add buttons for selecting an image and running the analysis
    ttk.Button(cobb_buttons_frame, text="Select X-ray Image", command=select_xray_for_cobb).pack(side='left', padx=5)
    ttk.Button(cobb_buttons_frame, text="Analyze Cobb Angles", command=analyze_cobb_angle).pack(side='left', padx=5)

    # Add a frame for displaying the selected image and results
    cobb_display_frame = ttk.Frame(cobb_frame, style='TFrame')
    cobb_display_frame.pack(fill='both', expand=True, pady=10)

    # Configure grid layout
    cobb_display_frame.columnconfigure(0, weight=1)
    cobb_display_frame.columnconfigure(1, weight=1)
    cobb_display_frame.rowconfigure(0, weight=0)
    cobb_display_frame.rowconfigure(1, weight=1)

    # Add result label
    cobb_result_label = ttk.Label(cobb_display_frame, text="No analysis performed yet",
                                font=('Segoe UI', 12, 'bold'))
    cobb_result_label.grid(row=0, column=0, columnspan=2, sticky='w', pady=(0, 10))

    # Add image display areas
    cobb_image_frame = ttk.LabelFrame(cobb_display_frame, text="Original X-ray")
    cobb_image_frame.grid(row=1, column=0, padx=5, pady=5, sticky='nsew')

    cobb_image_label = ttk.Label(cobb_image_frame, text="Select an image to begin")
    cobb_image_label.pack(fill='both', expand=True, padx=5, pady=5)

    # Add a frame for the analysis results
    cobb_results_frame = ttk.Frame(cobb_display_frame, style='TFrame')
    cobb_results_frame.grid(row=1, column=1, padx=5, pady=5, sticky='nsew')

    # Configure grid for results
    cobb_results_frame.columnconfigure(0, weight=1)
    cobb_results_frame.rowconfigure(0, weight=1)
    cobb_results_frame.rowconfigure(1, weight=1)

    # Add vertebrae and Cobb angle visualizations
    vertebrae_frame = ttk.LabelFrame(cobb_results_frame, text="Vertebrae Detection")
    vertebrae_frame.grid(row=0, column=0, padx=5, pady=5, sticky='nsew')

    cobb_vertebrae_label = ttk.Label(vertebrae_frame, text="Run analysis to view results")
    cobb_vertebrae_label.pack(fill='both', expand=True, padx=5, pady=5)

    angles_frame = ttk.LabelFrame(cobb_results_frame, text="Cobb Angle Measurement")
    angles_frame.grid(row=1, column=0, padx=5, pady=5, sticky='nsew')

    cobb_angles_label = ttk.Label(angles_frame, text="Run analysis to view results")
    cobb_angles_label.pack(fill='both', expand=True, padx=5, pady=5)

    # 2. Progression Tracking Tab
    progression_tab = ttk.Frame(advanced_notebook, style='TFrame')
    advanced_notebook.add(progression_tab, text='Progression Tracking')

    # Create content for progression tracking tab
    progression_frame = ttk.Frame(progression_tab, style='TFrame')
    progression_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Add instructions
    ttk.Label(progression_frame, text="Track scoliosis progression over time",
            font=('Segoe UI', 12, 'bold')).pack(anchor='w', pady=(0, 10))

    ttk.Label(progression_frame, text="Select a patient to view their progression history or add a new examination.").pack(anchor='w', pady=(0, 20))

    # Add patient selection
    patient_frame = ttk.Frame(progression_frame, style='TFrame')
    patient_frame.pack(fill='x', pady=10)

    ttk.Label(patient_frame, text="Patient:").pack(side='left', padx=5)

    # Create a variable to store the selected patient
    patient_var = tk.StringVar()

    # Create a function to populate the patient dropdown
    def populate_patient_dropdown():
        """Populate the patient dropdown with patients from the database"""
        try:
            # Get database instance
            db = get_db_instance()

            # Get current user ID
            from user_database import get_current_user_id, is_authenticated
            user_id = get_current_user_id() if is_authenticated() else None

            if not user_id:
                print("No user authenticated, not populating dropdown")
                patient_dropdown['values'] = []
                return

            # Get all patients for the current user
            patients = db.get_all_patients()

            # Format patients for dropdown
            formatted_patients = []
            for patient in patients:
                formatted_patients.append(f"{patient['name']} (ID: {patient['id']})")

            # Update the dropdown
            patient_dropdown['values'] = formatted_patients

            if formatted_patients:
                patient_dropdown.current(0)

            print(f"Populated dropdown with {len(formatted_patients)} patients for user {user_id}")
        except Exception as e:
            print(f"Error populating patient dropdown: {e}")

    # Create a function to add a new patient
    def add_new_patient():
        """Add a new patient to the database"""
        # Ask for patient information
        patient_name = sd.askstring("New Patient", "Enter patient name:")
        if not patient_name:
            return

        # Ask for date of birth
        dob = sd.askstring("New Patient", "Enter date of birth (YYYY-MM-DD):")

        # Ask for gender
        gender = sd.askstring("New Patient", "Enter gender (M/F):")

        try:
            # Initialize the tracker
            tracker = ProgressionTracker()

            # Add the patient
            patient_id = tracker.add_patient(patient_name, dob, gender)

            # Update the dropdown
            populate_patient_dropdown()

            # Select the new patient
            patient_dropdown.set(f"{patient_name} (ID: {patient_id})")

            mb.showinfo("Success", f"Patient {patient_name} added successfully.")
        except Exception as e:
            mb.showerror("Error", f"Error adding patient: {str(e)}")

    # Create a function to add a new examination
    def add_new_examination():
        """Add a new examination for the selected patient"""
        # Get the selected patient ID
        selected = patient_var.get()
        if not selected:
            mb.showerror("Error", "Please select a patient first.")
            return

        # Extract the patient ID from the selection
        import re
        match = re.search(r"ID: (\d+)", selected)
        if not match:
            mb.showerror("Error", "Invalid patient selection.")
            return

        patient_id = int(match.group(1))

        # Check if we have vertebrae data from Cobb angle analysis
        global current_vertebrae_data
        if not current_vertebrae_data:
            mb.showerror("Error", "Please perform Cobb angle analysis first.")
            return

        # Ask for examination date
        date = sd.askstring("New Examination", "Enter examination date (YYYY-MM-DD):",
                          initialvalue=datetime.datetime.now().strftime("%Y-%m-%d"))
        if not date:
            return

        # Ask for notes
        notes = sd.askstring("New Examination", "Enter any notes about this examination:")

        try:
            # Initialize the tracker
            tracker = ProgressionTracker()

            # Add the examination
            examination_id = tracker.add_examination(
                patient_id,
                date,
                current_vertebrae_data["image_path"],
                current_vertebrae_data,
                notes
            )

            mb.showinfo("Success", f"Examination added successfully.")

            # Refresh the progression chart
            view_progression()
        except Exception as e:
            mb.showerror("Error", f"Error adding examination: {str(e)}")

    # Create a function to view progression
    def view_progression():
        """View progression for the selected patient"""
        # Get the selected patient ID
        selected = patient_var.get()
        if not selected:
            mb.showerror("Error", "Please select a patient first.")
            return

        # Extract the patient ID from the selection
        import re
        match = re.search(r"ID: (\d+)", selected)
        if not match:
            mb.showerror("Error", "Invalid patient selection.")
            return

        patient_id = int(match.group(1))

        # Show a progress message
        update_status("Generating progression chart... This may take a moment.")

        # Run the analysis in a separate thread to keep the UI responsive
        def run_progression_analysis():
            try:
                # Call the track_progression function
                results = track_progression(patient_id)

                # Update the UI with the results
                root.after(0, lambda: display_progression_results(results))
            except Exception as e:
                root.after(0, lambda: mb.showerror("Error", f"Error tracking progression: {str(e)}"))
                root.after(0, lambda: update_status("Progression tracking failed."))

        threading.Thread(target=run_progression_analysis).start()

    def display_progression_results(results):
        """Display the progression tracking results"""
        # Update status
        update_status("Progression tracking complete!")

        if "error" in results:
            progression_result_label.config(text=results["error"])
            return

        # Display the progression information
        if "progression_rate" in results:
            rate = results["progression_rate"]["progression_rate"]
            risk = results["progression_rate"]["risk_category"]
            result_text = f"Progression Rate: {rate:.2f}°/year | Risk Category: {risk}"
            progression_result_label.config(text=result_text)
        else:
            progression_result_label.config(text="No progression data available.")

        # Display the progression chart
        try:
            img = Image.open(results["output_files"]["progression_chart"])
            img.thumbnail((600, 400))
            photo = ImageTk.PhotoImage(img)
            progression_chart_label.config(image=photo)
            progression_chart_label.image = photo
        except Exception as e:
            print(f"Error displaying progression chart: {e}")

        # Display the X-ray comparison if available
        if "xray_comparison" in results["output_files"]:
            try:
                img = Image.open(results["output_files"]["xray_comparison"])
                img.thumbnail((600, 300))
                photo = ImageTk.PhotoImage(img)
                progression_comparison_label.config(image=photo)
                progression_comparison_label.image = photo
            except Exception as e:
                print(f"Error displaying X-ray comparison: {e}")

    # Create the patient dropdown
    patient_dropdown = ttk.Combobox(patient_frame, textvariable=patient_var, width=30)
    patient_dropdown.pack(side='left', padx=5)

    # Add buttons for patient management
    ttk.Button(patient_frame, text="Add New Patient", command=add_new_patient).pack(side='left', padx=5)
    ttk.Button(patient_frame, text="Refresh", command=populate_patient_dropdown).pack(side='left', padx=5)

    # Add buttons for examination management
    examination_frame = ttk.Frame(progression_frame, style='TFrame')
    examination_frame.pack(fill='x', pady=10)

    ttk.Button(examination_frame, text="Add New Examination", command=add_new_examination).pack(side='left', padx=5)
    ttk.Button(examination_frame, text="View Progression", command=view_progression).pack(side='left', padx=5)

    # Add a frame for displaying the progression results
    progression_display_frame = ttk.Frame(progression_frame, style='TFrame')
    progression_display_frame.pack(fill='both', expand=True, pady=10)

    # Add result label
    progression_result_label = ttk.Label(progression_display_frame, text="No progression data available",
                                      font=('Segoe UI', 12, 'bold'))
    progression_result_label.pack(anchor='w', pady=(0, 10))

    # Add chart display
    progression_chart_frame = ttk.LabelFrame(progression_display_frame, text="Progression Chart")
    progression_chart_frame.pack(fill='both', expand=True, padx=5, pady=5)

    progression_chart_label = ttk.Label(progression_chart_frame, text="Select a patient and click 'View Progression'")
    progression_chart_label.pack(fill='both', expand=True, padx=5, pady=5)

    # Add X-ray comparison display
    progression_comparison_frame = ttk.LabelFrame(progression_display_frame, text="X-ray Comparison")
    progression_comparison_frame.pack(fill='both', expand=True, padx=5, pady=5)

    progression_comparison_label = ttk.Label(progression_comparison_frame, text="Need at least two examinations to compare")
    progression_comparison_label.pack(fill='both', expand=True, padx=5, pady=5)

    # Populate the patient dropdown
    populate_patient_dropdown()

    # 3. 3D Reconstruction Tab
    reconstruction_tab = ttk.Frame(advanced_notebook, style='TFrame')
    advanced_notebook.add(reconstruction_tab, text='3D Reconstruction')

    # Create content for 3D reconstruction tab
    reconstruction_frame = ttk.Frame(reconstruction_tab, style='TFrame')
    reconstruction_frame.pack(fill='both', expand=True, padx=10, pady=10)

    # Add instructions
    ttk.Label(reconstruction_frame, text="Generate 3D spine models from 2D X-rays",
            font=('Segoe UI', 12, 'bold')).pack(anchor='w', pady=(0, 10))

    ttk.Label(reconstruction_frame, text="First perform Cobb angle analysis, then click 'Generate 3D Model' to create a 3D spine model.").pack(anchor='w', pady=(0, 20))

    # Add button for generating 3D model
    reconstruction_buttons_frame = ttk.Frame(reconstruction_frame, style='TFrame')
    reconstruction_buttons_frame.pack(fill='x', pady=10)

    def generate_3d_model():
        """Generate a 3D spine model from the current vertebrae data"""
        # Check if we have vertebrae data from Cobb angle analysis
        global current_vertebrae_data
        if not current_vertebrae_data:
            mb.showerror("Error", "Please perform Cobb angle analysis first.")
            return

        # Show a progress message
        update_status("Generating 3D model... This may take a moment.")
        reconstruction_result_label.config(text="3D reconstruction in progress...")

        # Run the reconstruction in a separate thread to keep the UI responsive
        def run_reconstruction():
            try:
                # Call the reconstruct_spine_3d function
                results = reconstruct_spine_3d(current_vertebrae_data)

                # Update the UI with the results
                root.after(0, lambda: display_reconstruction_results(results))
            except Exception as e:
                root.after(0, lambda: mb.showerror("Error", f"Error generating 3D model: {str(e)}"))
                root.after(0, lambda: update_status("3D reconstruction failed."))

        threading.Thread(target=run_reconstruction).start()

    def display_reconstruction_results(results):
        """Display the 3D reconstruction results"""
        # Update status
        update_status("3D reconstruction complete!")
        reconstruction_result_label.config(text="3D model generated successfully")

        # Display the static visualization
        try:
            img = Image.open(results["output_files"]["static_visualization"])
            img.thumbnail((400, 400))
            photo = ImageTk.PhotoImage(img)
            reconstruction_static_label.config(image=photo)
            reconstruction_static_label.image = photo
        except Exception as e:
            print(f"Error displaying static visualization: {e}")

        # Display the animation
        try:
            # For GIF animations, we need to use a different approach
            animation_path = results["output_files"]["animation"]

            # Create a button to open the animation in a separate window
            def open_animation():
                try:
                    if sys.platform == 'win32':
                        os.startfile(animation_path)
                    else:
                        import subprocess
                        subprocess.call(['xdg-open', animation_path])
                except Exception as e:
                    mb.showerror("Error", f"Error opening animation: {str(e)}")

            # Create a thumbnail of the first frame
            img = Image.open(animation_path)
            img.thumbnail((400, 400))
            photo = ImageTk.PhotoImage(img)

            # Create a button with the thumbnail
            animation_button = ttk.Button(reconstruction_animation_frame, image=photo, command=open_animation)
            animation_button.image = photo
            animation_button.pack(fill='both', expand=True, padx=5, pady=5)

            # Add a label with instructions
            ttk.Label(reconstruction_animation_frame, text="Click to open animation").pack(pady=5)
        except Exception as e:
            print(f"Error displaying animation: {e}")

    ttk.Button(reconstruction_buttons_frame, text="Generate 3D Model", command=generate_3d_model).pack(side='left', padx=5)

    # Add a frame for displaying the reconstruction results
    reconstruction_display_frame = ttk.Frame(reconstruction_frame, style='TFrame')
    reconstruction_display_frame.pack(fill='both', expand=True, pady=10)

    # Add result label
    reconstruction_result_label = ttk.Label(reconstruction_display_frame, text="No 3D model generated yet",
                                         font=('Segoe UI', 12, 'bold'))
    reconstruction_result_label.pack(anchor='w', pady=(0, 10))

    # Create a frame for the static and animated visualizations
    reconstruction_visuals_frame = ttk.Frame(reconstruction_display_frame, style='TFrame')
    reconstruction_visuals_frame.pack(fill='both', expand=True)

    # Configure grid layout
    reconstruction_visuals_frame.columnconfigure(0, weight=1)
    reconstruction_visuals_frame.columnconfigure(1, weight=1)
    reconstruction_visuals_frame.rowconfigure(0, weight=1)

    # Add static visualization
    reconstruction_static_frame = ttk.LabelFrame(reconstruction_visuals_frame, text="3D Spine Model")
    reconstruction_static_frame.grid(row=0, column=0, padx=5, pady=5, sticky='nsew')

    reconstruction_static_label = ttk.Label(reconstruction_static_frame, text="Generate a 3D model to view")
    reconstruction_static_label.pack(fill='both', expand=True, padx=5, pady=5)

    # Add animation visualization
    reconstruction_animation_frame = ttk.LabelFrame(reconstruction_visuals_frame, text="Rotating Animation")
    reconstruction_animation_frame.grid(row=0, column=1, padx=5, pady=5, sticky='nsew')

    reconstruction_animation_label = ttk.Label(reconstruction_animation_frame, text="Generate a 3D model to view animation")
    reconstruction_animation_label.pack(fill='both', expand=True, padx=5, pady=5)

    # No need to re-initialize current_vertebrae_data as it's already defined globally

# Create reports content
reports_frame = ttk.Frame(reports_tab, style='TFrame')
reports_frame.pack(fill='both', expand=True, padx=20, pady=20)

# Add a header
reports_header = ttk.Label(reports_frame, text="Patient Reports", style='Subheader.TLabel')
reports_header.pack(anchor='w', pady=(0, 20))

# Add description
reports_desc = ttk.Label(reports_frame, text="View, export, and manage patient reports. You can filter reports by diagnosis or search by patient name.")
reports_desc.pack(anchor='w', pady=(0, 20))

# Add report options
report_options_frame = ttk.Frame(reports_frame, style='TFrame')
report_options_frame.pack(fill='x', pady=10)

# Add buttons to view reports
def open_report_viewer():
    """Open the report viewer"""
    try:
        subprocess.Popen([sys.executable, "report_viewer.py"])
    except Exception as e:
        mb.showerror("Error", f"Error opening report viewer: {str(e)}")

view_reports_btn = ttk.Button(report_options_frame, text="View All Reports", command=open_report_viewer)
view_reports_btn.pack(side='left', padx=5, pady=5)

def view_recent_reports():
    """View recent reports"""
    try:
        subprocess.Popen([sys.executable, "report_viewer.py", "--recent"])
    except Exception as e:
        mb.showerror("Error", f"Error opening report viewer: {str(e)}")

recent_reports_btn = ttk.Button(report_options_frame, text="View Recent Reports", command=view_recent_reports)
recent_reports_btn.pack(side='left', padx=5, pady=5)

# Define the refresh_reports_list function
def refresh_reports_list():
    """Refresh the list of reports in the Reports tab"""
    # Clear the current reports list
    for widget in reports_frame.winfo_children():
        if widget != reports_header and widget != reports_desc and widget != report_options_frame:
            widget.destroy()

    # Check if reports directory exists
    if not os.path.exists("reports"):
        no_reports_label = ttk.Label(reports_frame,
                                   text="No reports found. Generate a report first.",
                                   font=('Segoe UI', 12))
        no_reports_label.pack(pady=50)
        return

    # Get all report files
    report_files = []
    for file in os.listdir("reports"):
        if file.endswith(".html") or file.endswith(".pdf"):
            report_files.append(file)

    if not report_files:
        no_reports_label = ttk.Label(reports_frame,
                                   text="No reports found. Generate a report first.",
                                   font=('Segoe UI', 12))
        no_reports_label.pack(pady=50)
        return

    # Create a scrollable frame for reports
    reports_canvas = tk.Canvas(reports_frame, bg=BG_COLOR)
    scrollbar = ttk.Scrollbar(reports_frame, orient="vertical", command=reports_canvas.yview)
    scrollable_frame = ttk.Frame(reports_canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: reports_canvas.configure(scrollregion=reports_canvas.bbox("all"))
    )

    reports_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    reports_canvas.configure(yscrollcommand=scrollbar.set)

    reports_canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Add a header
    header_frame = ttk.Frame(scrollable_frame)
    header_frame.pack(fill="x", padx=20, pady=10)

    ttk.Label(header_frame, text="Patient Reports",
            font=('Segoe UI', 16, 'bold')).pack(anchor="w")

    ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=20)

    # Add each report to the list in ascending order by date (oldest first)
    for file in sorted(report_files):
        # Extract patient ID, name and date from filename
        parts = file.replace('.html', '').replace('.pdf', '').split('_')
        if len(parts) >= 3:  # Ensure we have at least patient_id, patient_name, and timestamp
            patient_id = parts[0]
            patient_name = parts[1].replace('_', ' ')

            # Try to parse the timestamp
            try:
                if len(parts[-1]) == 8:  # YYYYMMDD format
                    date_str = f"{parts[-1][:4]}-{parts[-1][4:6]}-{parts[-1][6:8]}"
                    time_str = "00:00"  # Default time if not provided
                elif len(parts[-1]) >= 14:  # YYYYMMDDHHMMSS format
                    timestamp = parts[-1]
                    date_str = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]}"
                    time_str = f"{timestamp[8:10]}:{timestamp[10:12]}"
                elif len(parts) >= 4 and len(parts[-2] + parts[-1]) >= 14:  # YYYYMMDD_HHMMSS format
                    timestamp = parts[-2] + parts[-1]
                    date_str = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]}"
                    time_str = f"{timestamp[8:10]}:{timestamp[10:12]}"
                else:
                    date_str = "Unknown date"
                    time_str = "Unknown time"
            except:
                date_str = "Unknown date"
                time_str = "Unknown time"

            # Create a frame for this report
            report_frame = ttk.Frame(scrollable_frame)
            report_frame.pack(fill="x", padx=20, pady=5)

            # Add report info
            info_frame = ttk.Frame(report_frame)
            info_frame.pack(side="left", fill="x", expand=True)

            ttk.Label(info_frame, text=f"Patient ID: {patient_id}",
                    font=('Segoe UI', 10)).pack(anchor="w")
            ttk.Label(info_frame, text=f"Patient Name: {patient_name}",
                    font=('Segoe UI', 12, 'bold')).pack(anchor="w")
            ttk.Label(info_frame, text=f"Date: {date_str}",
                    font=('Segoe UI', 10)).pack(anchor="w")
            ttk.Label(info_frame, text=f"Time: {time_str}",
                    font=('Segoe UI', 10)).pack(anchor="w")

            # Add buttons to view the report
            button_frame = ttk.Frame(report_frame)
            button_frame.pack(side="right")

            # Determine file type and create appropriate button
            if file.endswith(".html"):
                def open_html_report(file_path=os.path.join("reports", file)):
                    try:
                        webbrowser.open('file://' + os.path.abspath(file_path))
                    except Exception as e:
                        mb.showerror("Error", f"Error opening HTML report: {str(e)}")

                ttk.Button(button_frame, text="View HTML",
                         command=open_html_report).pack(side="right", padx=5)

            if file.endswith(".pdf"):
                def open_pdf_report(file_path=os.path.join("reports", file)):
                    try:
                        # Check if the file exists
                        if not os.path.exists(file_path):
                            mb.showerror("Error", f"PDF file not found: {file_path}")
                            return

                        # Get the absolute path to ensure it works correctly
                        abs_path = os.path.abspath(file_path)
                        print(f"Opening PDF report: {abs_path}")

                        if sys.platform == 'win32':
                            os.startfile(abs_path)
                        elif sys.platform == 'darwin':  # macOS
                            subprocess.call(['open', abs_path])
                        else:  # Linux
                            subprocess.call(['xdg-open', abs_path])
                    except Exception as e:
                        mb.showerror("Error", f"Error opening PDF report: {str(e)}")
                        print(f"Error details: {e}")

                ttk.Button(button_frame, text="View PDF",
                         command=open_pdf_report).pack(side="right", padx=5)

            # Add a separator
            ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=20, pady=5)

# Add a refresh button
refresh_btn = ttk.Button(report_options_frame, text="Refresh Reports List", command=refresh_reports_list)
refresh_btn.pack(side='right', padx=5, pady=5)

# Initialize the reports list
refresh_reports_list()

# Status bar at the bottom
status_frame = ttk.Frame(root, style='TFrame', height=30)
status_frame.pack(side='bottom', fill='x')

# Add a separator above the status bar
ttk.Separator(root, orient='horizontal').pack(side='bottom', fill='x')

# Status label
status_label = ttk.Label(status_frame, text="Ready", font=('Segoe UI', 10))
status_label.pack(side='left', padx=10, pady=5)

# Update functions for the UI
def update_status(message):
    """Update the status bar with a message"""
    status_label.config(text=message)

def update_results(message):
    """Update the results label with analysis results"""
    results_label.config(text=message)

def update_label(str_T):
    """Update the results label with analysis results and status bar"""
    # Update the results label
    update_results(str_T)

    # Update the status bar
    if "Error" in str_T:
        update_status("Error: " + str_T.split("Error")[1].strip() if "Error" in str_T else str_T)
    elif "Completed" in str_T:
        update_status("Ready - Analysis complete")
    elif "Start" in str_T:
        update_status("Processing - Please wait...")
    else:
        update_status(str_T.split('\n')[0] if '\n' in str_T else str_T)

def openimage():
    """Open and display an X-ray image"""
    global fn, current_image_path, current_report_path, current_html_report_path

    # Show file dialog to select an image
    fileName = filedialog.askopenfilename(
        initialdir='.',  # Start in current directory
        title='Select X-ray Image for Analysis',
        filetypes=[
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tif *.tiff"),
            ("All files", "*.*")
        ]
    )

    if not fileName:  # User canceled the file dialog
        return

    # Clear previous report paths if a new image is selected
    if fileName != current_image_path:
        current_image_path = fileName
        current_report_path = ""
        current_html_report_path = ""
        update_label("New image selected. Please run prediction to generate a new report.")

    IMAGE_SIZE = 400  # Larger image size for better visibility
    imgpath = fileName
    fn = fileName

    try:
        # Open and resize the image
        img = Image.open(imgpath)

        # Calculate aspect ratio to maintain proportions
        width, height = img.size
        aspect_ratio = width / height
        new_height = int(IMAGE_SIZE / aspect_ratio) if aspect_ratio > 1 else IMAGE_SIZE
        new_width = IMAGE_SIZE if aspect_ratio > 1 else int(IMAGE_SIZE * aspect_ratio)

        img = img.resize((new_width, new_height))

        # Create PhotoImage for display
        img_display = ImageTk.PhotoImage(img)

        # Clear any existing widgets in the original frame
        for widget in original_frame.winfo_children():
            widget.destroy()

        # Display the image in the original frame
        img_label = ttk.Label(original_frame, image=img_display)
        img_label.image = img_display  # Keep a reference to prevent garbage collection
        img_label.pack(padx=10, pady=10, fill='both', expand=True)

        # Clear the grayscale and binary frames
        for widget in gray_frame.winfo_children():
            widget.destroy()

        for widget in binary_frame.winfo_children():
            widget.destroy()

        # Add placeholder text to the empty frames
        ttk.Label(gray_frame, text="Click 'Image Preprocessing' to view", font=('Segoe UI', 10)).pack(pady=50)
        ttk.Label(binary_frame, text="Click 'Image Preprocessing' to view", font=('Segoe UI', 10)).pack(pady=50)

        # Update status
        update_status(f"Loaded image: {os.path.basename(fileName)}")

        # Switch to the Image Analysis tab
        tab_control.select(0)  # Select the first tab (Image Analysis)

    except Exception as e:
        mb.showerror("Error", f"Error opening image: {str(e)}")
        update_label(f"Error opening image: {str(e)}")
        update_status(f"Error: Failed to open image")

def preprocess_image():
    """Process the image to create grayscale and binary versions"""
    global fn, current_image_path

    # Make sure we're using the current image
    if fn != current_image_path and current_image_path != "":
        fn = current_image_path

    if not fn or not os.path.exists(fn):
        update_label("Please select an image first")
        update_status("Error: No image selected")
        return

    try:
        # Update status
        update_status("Processing image...")

        # Get the original image
        img = Image.open(fn)

        # Calculate aspect ratio to maintain proportions
        IMAGE_SIZE = 300
        width, height = img.size
        aspect_ratio = width / height
        new_height = int(IMAGE_SIZE / aspect_ratio) if aspect_ratio > 1 else IMAGE_SIZE
        new_width = IMAGE_SIZE if aspect_ratio > 1 else int(IMAGE_SIZE * aspect_ratio)

        # Resize the image
        img = img.resize((new_width, new_height))

        # Create grayscale version using PIL
        gray_img = img.convert('L')

        # Create enhanced version (increase contrast)
        enhancer = ImageEnhance.Contrast(gray_img)
        enhanced_img = enhancer.enhance(1.5)  # Increase contrast by 50%

        # Create binary version (threshold)
        threshold_value = 128  # Middle value for thresholding
        binary_img = gray_img.point(lambda x: 0 if x < threshold_value else 255, '1')

        # Clear the grayscale frame
        for widget in gray_frame.winfo_children():
            widget.destroy()

        # Display grayscale image
        gray_photo = ImageTk.PhotoImage(gray_img)
        gray_label = ttk.Label(gray_frame, image=gray_photo)
        gray_label.image = gray_photo  # Keep a reference to prevent garbage collection
        gray_label.pack(padx=10, pady=10, fill='both', expand=True)

        # Clear the binary frame
        for widget in binary_frame.winfo_children():
            widget.destroy()

        # Display binary image
        binary_photo = ImageTk.PhotoImage(binary_img)
        binary_label = ttk.Label(binary_frame, image=binary_photo)
        binary_label.image = binary_photo  # Keep a reference to prevent garbage collection
        binary_label.pack(padx=10, pady=10, fill='both', expand=True)

        # Clear the original frame and display the enhanced image
        for widget in original_frame.winfo_children():
            widget.destroy()

        enhanced_photo = ImageTk.PhotoImage(enhanced_img)
        enhanced_label = ttk.Label(original_frame, image=enhanced_photo)
        enhanced_label.image = enhanced_photo  # Keep a reference to prevent garbage collection
        enhanced_label.pack(padx=10, pady=10, fill='both', expand=True)

        # Update status
        update_status("Image preprocessing complete")
        update_label("Image preprocessing complete. Ready for analysis.")

    except Exception as e:
        mb.showerror("Error", f"Error processing image: {str(e)}")
        update_label(f"Error processing image: {str(e)}")
        update_status(f"Error: Failed to process image")

def test_model():
    global fn, current_report_path, current_html_report_path, prediction_confidence, diagnosis_text, current_image_path
    if fn != "":
        # Clear any existing reports when a new prediction is made
        current_report_path = ""
        current_html_report_path = ""

        update_label("Analysis in progress...")
        update_status("Processing - Please wait...")

        # Simulate analysis (in a real app, this would call your model)
        is_scoliosis = random.choice([True, False])
        confidence = random.uniform(85.0, 99.9)

        # Store for report generation
        prediction_confidence = confidence

        if is_scoliosis:
            diagnosis = "Scoliosis Detected \nWork with a physical therapist for customized exercises"
        else:
            diagnosis = "No Scoliosis Detected \nMaintain healthy diet and exercise routine"

        # Store the diagnosis for report generation
        diagnosis_text = diagnosis

        # Save the current image path for patient history
        current_image_path = fn

        result_message = f"Analysis Complete\n\nDiagnosis: {diagnosis}\n\nConfidence: {confidence:.2f}%"
        update_label(result_message)
        update_status("Analysis complete")

        # Save the analysis to patient history
        try:
            # Ask for patient information
            patient_info = ask_for_patient_info()
            if patient_info:
                patient_id, patient_name = patient_info

                # Get database instance
                db = get_db_instance()

                # Check if patient exists, add if not
                if not db.patient_exists(patient_id):
                    db.add_patient(patient_id, patient_name)

                # Add the examination to the database
                exam_id = db.add_examination(
                    patient_id=patient_id,
                    diagnosis=diagnosis,
                    confidence=confidence,
                    image_path=current_image_path,
                    notes=f"Analysis performed on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                # Store the patient information for later use
                global current_patient_id, current_patient_name
                current_patient_id = patient_id
                current_patient_name = patient_name

                # Always refresh the patient history tab when a new examination is added
                # This ensures the patient history is up-to-date regardless of which tab is currently selected

                def refresh_patient_history():
                    """Function to refresh patient history after database update"""
                    # Force a direct refresh of the database connection to ensure we get fresh data
                    # This is critical for ensuring we see the latest data
                    import patient_database
                    patient_database._instance = None  # Reset the singleton instance

                    # Now get a fresh database instance
                    db = get_db_instance()
                    print(f"Forced database refresh after adding examination for {patient_name}")

                    # Force a complete refresh of the patient history tab
                    refresh_history_tab()
                    print(f"Forced complete refresh of patient history tab")

                    # If we're on the Patient History tab, update the view accordingly
                    if tab_control.index(tab_control.select()) == 1:
                        # We're on the Patient History tab
                        # Check if we're viewing this patient's history
                        patient_detail_view = False
                        for widget in history_frame.winfo_children():
                            if isinstance(widget, ttk.Label) and hasattr(widget, 'cget') and patient_name in widget.cget("text"):
                                patient_detail_view = True
                                break

                        if patient_detail_view:
                            # We're already viewing this patient's history, refresh it
                            print(f"Refreshing patient detail view for {patient_name}")
                            display_patient_history(patient_id, patient_name)
                        else:
                            # We're in the patient list view, it's already refreshed by refresh_history_tab()
                            print("Patient list view already refreshed")
                    else:
                        # We're on a different tab, the history tab is already refreshed in the background
                        print("Patient history tab refreshed in background")

                # Immediate refresh of patient list
                refresh_patient_list()

                # Schedule the refresh to happen after a short delay to ensure database operations are complete
                root.after(500, refresh_patient_history)

                # Show confirmation with option to view history
                if mb.askyesno("Patient History",
                              "Analysis results have been added to patient history.\n\nWould you like to view the patient's history?"):
                    # Switch to the Patient History tab
                    tab_control.select(1)  # Select the Patient History tab
                    # Display the patient's history
                    display_patient_history(patient_id, patient_name)
        except Exception as e:
            mb.showerror("Error", f"Error saving to patient history: {str(e)}")

        # Ask if user wants to generate a report
        if mb.askyesno("Generate Report", "Would you like to generate a report for this diagnosis?"):
            try:
                generate_report()
            except Exception as e:
                mb.showerror("Error", f"Error generating report: {str(e)}")
    else:
        msg = "Please select an image first"
        update_label(msg)
        update_status("Error: No image selected")

def ask_for_patient_info():
    """Ask for patient information and return (patient_id, patient_name)"""
    # Create a custom dialog
    dialog = tk.Toplevel(root)
    dialog.title("Patient Information")
    dialog.transient(root)
    dialog.grab_set()

    # Set a fixed size for the dialog that ensures all elements are visible
    w = 450
    h = 250

    # Make the dialog non-resizable to maintain layout
    dialog.resizable(False, False)

    # Center the dialog
    x = (root.winfo_screenwidth() // 2) - (w // 2)
    y = (root.winfo_screenheight() // 2) - (h // 2)
    dialog.geometry(f"{w}x{h}+{x}+{y}")

    # Create a frame for the content with padding
    content_frame = ttk.Frame(dialog, padding=20)
    content_frame.pack(fill='both', expand=True)

    # Add a title
    title_label = ttk.Label(
        content_frame,
        text="Enter Patient Information",
        font=('Segoe UI', 14, 'bold')
    )
    title_label.pack(pady=(0, 20))

    # Patient ID
    id_frame = ttk.Frame(content_frame)
    id_frame.pack(fill='x', pady=5)

    id_label = ttk.Label(id_frame, text="Patient ID:", width=12, anchor='e')
    id_label.pack(side='left', padx=(0, 5))

    patient_id_var = tk.StringVar()
    patient_id_entry = ttk.Entry(id_frame, textvariable=patient_id_var, width=25)
    patient_id_entry.pack(side='left', padx=5, fill='x', expand=True)

    # Patient Name
    name_frame = ttk.Frame(content_frame)
    name_frame.pack(fill='x', pady=10)

    name_label = ttk.Label(name_frame, text="Patient Name:", width=12, anchor='e')
    name_label.pack(side='left', padx=(0, 5))

    patient_name_var = tk.StringVar()
    patient_name_entry = ttk.Entry(name_frame, textvariable=patient_name_var, width=30)
    patient_name_entry.pack(side='left', padx=5, fill='x', expand=True)

    # Add a separator
    ttk.Separator(content_frame, orient='horizontal').pack(fill='x', pady=15)

    # Buttons - place at the bottom with fixed height
    button_frame = ttk.Frame(content_frame)
    button_frame.pack(side='bottom', fill='x', pady=(0, 5))

    # Result variable
    result = [None, None]

    def on_ok():
        patient_id = patient_id_var.get().strip()
        patient_name = patient_name_var.get().strip()

        if not patient_id:
            mb.showerror("Error", "Please enter a patient ID.")
            return

        if not patient_name:
            mb.showerror("Error", "Please enter a patient name.")
            return

        result[0] = patient_id
        result[1] = patient_name
        dialog.destroy()

    def on_cancel():
        dialog.destroy()

    # Place buttons with right alignment
    cancel_btn = ttk.Button(button_frame, text="Cancel", command=on_cancel, width=10)
    cancel_btn.pack(side='right', padx=5)

    ok_btn = ttk.Button(button_frame, text="OK", command=on_ok, width=10)
    ok_btn.pack(side='right', padx=5)

    # Set focus to the first entry
    patient_id_entry.focus_set()

    # Wait for the dialog to be closed
    dialog.wait_window()

    # Return the result
    if result[0] and result[1]:
        return result
    else:
        return None

def generate_report():
    """Function to generate a report from the current image and diagnosis"""
    global fn, prediction_confidence, current_image_path, current_report_path, current_html_report_path, diagnosis_text

    if fn == "":
        update_label("Please select an image first")
        update_status("Error: No image selected")
        return

    try:
        # Get patient name
        patient_name = sd.askstring("Patient Information", "Enter patient name:")
        if not patient_name:
            return

        # Use the diagnosis from the analysis if available, otherwise generate a new one
        if not diagnosis_text:
            # Get diagnosis from the model (simulated)
            is_scoliosis = random.choice([True, False])

            if is_scoliosis:
                diagnosis = "Scoliosis Detected \nWork with a physical therapist for customized exercises"
            else:
                diagnosis = "No Scoliosis Detected \nMaintain healthy diet and exercise routine"
        else:
            diagnosis = diagnosis_text

        # Create recommendations based on diagnosis
        if "Scoliosis Detected" in diagnosis:
            recommendations = [
                "Work with a physical therapist for customized exercises",
                "Schedule a follow-up appointment in 3 months",
                "Consider postural training to improve spinal alignment",
                "Maintain regular exercise routine focusing on core strength"
            ]
        else:
            recommendations = [
                "Maintain healthy diet and exercise routine",
                "Practice good posture during daily activities",
                "Schedule routine check-up in 6 months",
                "Stay hydrated for spinal disc health"
            ]

        # Update status
        update_status("Generating report...")

        # Use the existing report_system.py
        try:
            from report_system import ReportGenerator

            # Create a report generator instance
            report_gen = ReportGenerator(patient_name)

            # Generate PDF report
            current_report_path = report_gen.generate_report(
                diagnosis=diagnosis,
                image_path=fn,
                confidence_score=prediction_confidence,
                recommendations=recommendations
            )

            # Generate HTML report
            current_html_report_path = report_gen.generate_html_report(
                diagnosis=diagnosis,
                image_path=fn,
                confidence_score=prediction_confidence,
                recommendations=recommendations
            )

            # Save report to database
            try:
                report_gen.save_to_database(diagnosis, fn)

                # Update the examination in the patient database with report paths
                if current_patient_id:
                    try:
                        # Get database instance
                        db = get_db_instance()

                        # Get the most recent examination for this patient
                        examinations = db.get_patient_examinations(current_patient_id)
                        if examinations:
                            latest_exam = examinations[0]  # First one is the most recent

                            # Update the examination with report paths
                            db.cursor.execute(
                                "UPDATE examinations SET report_path = ?, html_report_path = ? WHERE id = ?",
                                (current_report_path, current_html_report_path, latest_exam['id'])
                            )
                            db.conn.commit()

                            # Always refresh the patient history tab when a report is added
                            # This ensures the patient history is up-to-date regardless of which tab is currently selected

                            def refresh_patient_history_after_report():
                                """Function to refresh patient history after report generation"""
                                # Force a direct refresh of the database connection to ensure we get fresh data
                                # This is critical for ensuring we see the latest data
                                import patient_database
                                patient_database._instance = None  # Reset the singleton instance

                                # Now get a fresh database instance
                                db = get_db_instance()
                                print(f"Forced database refresh after adding report for {patient_name}")

                                # Force a complete refresh of the patient history tab
                                refresh_history_tab()
                                print(f"Forced complete refresh of patient history tab after report generation")

                                # If we're on the Patient History tab, update the view accordingly
                                if tab_control.index(tab_control.select()) == 1:
                                    # We're on the Patient History tab
                                    # Check if we're viewing this patient's history
                                    patient_detail_view = False
                                    for widget in history_frame.winfo_children():
                                        if isinstance(widget, ttk.Label) and hasattr(widget, 'cget') and patient_name in widget.cget("text"):
                                            patient_detail_view = True
                                            break

                                    if patient_detail_view and current_patient_id:
                                        # We're already viewing this patient's history, refresh it
                                        print(f"Refreshing patient detail view for {patient_name} after report generation")
                                        display_patient_history(current_patient_id, patient_name)
                                    else:
                                        # We're in the patient list view, it's already refreshed by refresh_history_tab()
                                        print("Patient list view already refreshed after report generation")
                                else:
                                    # We're on a different tab, the history tab is already refreshed in the background
                                    print("Patient history tab refreshed in background after report generation")

                            # Immediate refresh of patient list
                            refresh_patient_list()

                            # Schedule the refresh to happen after a short delay to ensure database operations are complete
                            root.after(500, refresh_patient_history_after_report)
                    except Exception as e:
                        print(f"Error updating examination with report paths: {e}")
            except Exception as db_error:
                print(f"Error saving report to database: {db_error}")

            # Update status
            update_status("Report generated successfully")

            # Show success message
            mb.showinfo("Report Generated",
                      f"Report for {patient_name} has been generated successfully.")

            # Show report options
            show_report_options(patient_name, diagnosis)

            return current_html_report_path

        except ImportError as e:
            mb.showerror("Error", f"Could not import report system: {str(e)}")
            update_status("Error: Report system not available")
            return None

    except Exception as e:
        mb.showerror("Error", f"Error generating report: {str(e)}")
        update_label(f"Error generating report: {str(e)}")
        update_status("Error generating report")
        return None

def show_report_options(patient_name, diagnosis):
    """Show options for viewing and sharing the report"""
    # Create a new window for report options
    options_window = tk.Toplevel(root)
    options_window.title("Report Options")
    options_window.geometry("400x300")
    options_window.resizable(False, False)
    options_window.transient(root)  # Make it a transient window (always on top of parent)
    options_window.grab_set()  # Make it modal (must be closed before interacting with parent)

    # Center the window
    options_window.update_idletasks()
    width = options_window.winfo_width()
    height = options_window.winfo_height()
    x = (options_window.winfo_screenwidth() // 2) - (width // 2)
    y = (options_window.winfo_screenheight() // 2) - (height // 2)
    options_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    # Style the window
    options_window.configure(bg=BG_COLOR)

    # Add header with patient info and diagnosis summary
    header_frame = tk.Frame(options_window, bg=BG_COLOR)
    header_frame.pack(fill="x", padx=20, pady=10)

    tk.Label(header_frame, text="Report Generated Successfully",
           font=("Segoe UI", 14, "bold"), bg=BG_COLOR, fg=PRIMARY_COLOR).pack()

    # Add patient info
    # Extract just the first part of the diagnosis (safely)
    diagnosis_parts = diagnosis.split('\n')[0] if '\n' in diagnosis else diagnosis
    info_text = f"Patient: {patient_name}\nDiagnosis: {diagnosis_parts}"
    tk.Label(header_frame, text=info_text, font=("Segoe UI", 10), justify="center", pady=5, bg=BG_COLOR).pack()

    # Add instruction
    tk.Label(header_frame, text="Please select an option below to view or share the report:",
           font=("Segoe UI", 10, "italic"), pady=5, bg=BG_COLOR).pack()

    # Add buttons
    button_frame = tk.Frame(options_window, bg=BG_COLOR)
    button_frame.pack(pady=10)

    # View PDF button
    def open_pdf():
        global current_report_path
        if current_report_path and os.path.exists(current_report_path):
            try:
                # Get the absolute path to ensure it works correctly
                abs_path = os.path.abspath(current_report_path)
                print(f"Opening PDF report: {abs_path}")

                # Open PDF with default application
                if sys.platform == 'win32':
                    os.startfile(abs_path)
                elif sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', abs_path])
                else:  # Linux
                    subprocess.call(['xdg-open', abs_path])
            except Exception as e:
                mb.showerror("Error", f"Error opening PDF report: {str(e)}")
                print(f"Error details: {e}")
        else:
            mb.showinfo("PDF Report", "PDF report is not available. Please view the HTML report instead.")

    pdf_btn = tk.Button(button_frame, text="Open PDF Report (Printable)", command=open_pdf,
                      bg=SECONDARY_COLOR, fg="white", font=("Segoe UI", 12), width=25, pady=5)
    pdf_btn.pack(pady=5)

    # View HTML button
    def open_html():
        global current_html_report_path
        if current_html_report_path and os.path.exists(current_html_report_path):
            try:
                # Open HTML in default browser
                webbrowser.open('file://' + os.path.abspath(current_html_report_path))
            except Exception as e:
                mb.showerror("Error", f"Error opening HTML report: {str(e)}")
        else:
            mb.showerror("Error", "HTML report file not found.")

    html_btn = tk.Button(button_frame, text="Open HTML Report (Interactive)", command=open_html,
                       bg=HIGHLIGHT_COLOR, fg="white", font=("Segoe UI", 12), width=25, pady=5)
    html_btn.pack(pady=5)

    # Email report button
    def email_report():
        options_window.destroy()
        try:
            # Import required modules
            from report_system import ReportGenerator
            from email_dialog import EmailDialog

            # Create ReportGenerator instance with existing report
            report_gen = ReportGenerator(patient_name)
            report_gen.filename = current_report_path

            # Show email dialog
            email_dialog = EmailDialog(root, report_gen)
            result = email_dialog.show()

            # Show result message if needed
            if result["success"]:
                mb.showinfo("Success", "Email sent successfully!")
        except Exception as e:
            mb.showerror("Email Error", f"Failed to send email: {str(e)}")

    email_btn = tk.Button(button_frame, text="Email Report to Patient", command=email_report,
                        bg=ACCENT_COLOR, fg="white", font=("Segoe UI", 12), width=25, pady=5)
    email_btn.pack(pady=5)

def view_reports():
    """View all patient reports"""
    # Switch to the Reports tab
    tab_control.select(2)  # Select the Reports tab

    # Refresh the reports list
    refresh_reports_list()

def refresh_reports_list():
    """Refresh the list of reports in the Reports tab"""
    # Clear the current reports list
    for widget in reports_frame.winfo_children():
        widget.destroy()

    # Add a refresh button at the top
    refresh_button_frame = ttk.Frame(reports_frame)
    refresh_button_frame.pack(fill="x", padx=10, pady=5)

    refresh_reports_btn = ttk.Button(
        refresh_button_frame,
        text="↻ Refresh Reports",
        command=lambda: trigger_refresh(root, refresh_reports_list)
    )
    refresh_reports_btn.pack(side="right", padx=5)

    # Check if reports directory exists
    if not os.path.exists("reports"):
        no_reports_label = ttk.Label(reports_frame,
                                   text="No reports found. Generate a report first.",
                                   font=('Segoe UI', 12))
        no_reports_label.pack(pady=50)
        return

    # Get only PDF report files
    report_files = []

    # Start with an empty list
    report_files = []

    # Try to get user-specific reports from the database first
    try:
        conn = sqlite3.connect('evaluation.db')
        cursor = conn.cursor()

        # Get current user ID from user_database module
        from user_database import get_current_user_id, is_authenticated

        # Get current user ID if authenticated
        user_id = get_current_user_id() if is_authenticated() else None

        if not user_id:
            print("No user authenticated, showing no reports")
            conn.close()
            return  # Return early, showing no reports for unauthenticated users

        print(f"Filtering reports for user: {user_id}")

        # Check if reports table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Check if user_id column exists in reports table
            cursor.execute("PRAGMA table_info(reports)")
            columns = [column[1] for column in cursor.fetchall()]
            has_user_id = 'user_id' in columns

            if has_user_id:
                # Get all report paths for the current user
                cursor.execute("SELECT report_path FROM reports WHERE user_id = ?", (user_id,))
                db_reports = cursor.fetchall()

                # Create a list of filenames (just the basename, not the full path)
                for report in db_reports:
                    if report[0] and os.path.exists(report[0]) and report[0].endswith(".pdf"):
                        report_files.append(os.path.basename(report[0]))

                print(f"Found {len(report_files)} reports for user {user_id}")

        conn.close()
    except Exception as e:
        print(f"Error filtering reports by user: {e}")

    # For new users, we don't fall back to showing all reports
    # This ensures that new users only see their own reports
    if not report_files:
        print(f"No reports found for user {user_id}")
        # We intentionally leave report_files empty

    if not report_files:
        no_reports_label = ttk.Label(reports_frame,
                                   text="No PDF reports found. Generate a report first.",
                                   font=('Segoe UI', 12))
        no_reports_label.pack(pady=50)
        return

    # Create a scrollable frame for reports
    reports_canvas = tk.Canvas(reports_frame, bg=BG_COLOR)
    scrollbar = ttk.Scrollbar(reports_frame, orient="vertical", command=reports_canvas.yview)
    scrollable_frame = ttk.Frame(reports_canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: reports_canvas.configure(scrollregion=reports_canvas.bbox("all"))
    )

    reports_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    reports_canvas.configure(yscrollcommand=scrollbar.set)

    reports_canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Add a header
    header_frame = ttk.Frame(scrollable_frame)
    header_frame.pack(fill="x", padx=20, pady=10)

    ttk.Label(header_frame, text="Patient Reports",
            font=('Segoe UI', 16, 'bold')).pack(anchor="w")

    ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=20)

    # Add each report to the list in ascending order by date (oldest first)
    for file in sorted(report_files):
        # Extract patient ID, name and date from filename
        parts = file.replace('.pdf', '').split('_')

        if len(parts) >= 3:  # Ensure we have at least patient_id, patient_name, and timestamp
            patient_id = parts[0]
            patient_name = parts[1].replace('_', ' ')

            # Try to parse the timestamp
            try:
                timestamp = parts[-1]
                if len(timestamp) == 8:  # YYYYMMDD format
                    date_str = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]}"
                    time_str = "00:00"  # Default time if not provided
                elif len(timestamp) >= 14:  # YYYYMMDDHHMMSS format
                    date_str = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]}"
                    time_str = f"{timestamp[8:10]}:{timestamp[10:12]}"
                else:
                    date_str = "Unknown date"
                    time_str = "Unknown time"
            except:
                date_str = "Unknown date"
                time_str = "Unknown time"

            # Create a frame for this report
            report_frame = ttk.Frame(scrollable_frame)
            report_frame.pack(fill="x", padx=20, pady=5)

            # Add report info
            info_frame = ttk.Frame(report_frame)
            info_frame.pack(side="left", fill="x", expand=True)

            ttk.Label(info_frame, text=f"Patient ID: {patient_id}",
                    font=('Segoe UI', 10)).pack(anchor="w")
            ttk.Label(info_frame, text=f"Patient Name: {patient_name}",
                    font=('Segoe UI', 12, 'bold')).pack(anchor="w")
            ttk.Label(info_frame, text=f"Date: {date_str}",
                    font=('Segoe UI', 10)).pack(anchor="w")
            ttk.Label(info_frame, text=f"Time: {time_str}",
                    font=('Segoe UI', 10)).pack(anchor="w")

            # Add button to view the PDF report
            button_frame = ttk.Frame(report_frame)
            button_frame.pack(side="right")

            def open_pdf_report(file_path=os.path.join("reports", file)):
                try:
                    # Check if the file exists
                    if not os.path.exists(file_path):
                        mb.showerror("Error", f"PDF file not found: {file_path}")
                        return

                    # Get the absolute path to ensure it works correctly
                    abs_path = os.path.abspath(file_path)
                    print(f"Opening PDF report: {abs_path}")

                    if sys.platform == 'win32':
                        os.startfile(abs_path)
                    elif sys.platform == 'darwin':  # macOS
                        subprocess.call(['open', abs_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', abs_path])
                except Exception as e:
                    mb.showerror("Error", f"Error opening PDF report: {str(e)}")
                    print(f"Error details: {e}")

            def delete_pdf_report(file_path=os.path.join("reports", file)):
                try:
                    # Confirm deletion
                    if not mb.askyesno("Confirm Deletion",
                                      f"Are you sure you want to delete this report for patient {patient_name}?\n\nThis action cannot be undone."):
                        return

                    # Check if the file exists
                    if not os.path.exists(file_path):
                        mb.showerror("Error", f"PDF file not found: {file_path}")
                        return

                    # Get the absolute path to ensure it works correctly
                    abs_path = os.path.abspath(file_path)
                    print(f"Deleting PDF report: {abs_path}")

                    # Import the ReportGenerator class to use its delete_report method
                    from report_system import ReportGenerator

                    # Delete the report
                    if ReportGenerator.delete_report(abs_path):
                        mb.showinfo("Success", f"Report for {patient_name} has been deleted.")
                        # Refresh the reports list
                        refresh_reports_list()
                    else:
                        mb.showerror("Error", f"Failed to delete report for {patient_name}. Please try again.")

                except Exception as e:
                    mb.showerror("Error", f"Error deleting PDF report: {str(e)}")
                    print(f"Error details: {e}")

            # Add View PDF button
            ttk.Button(button_frame, text="View PDF",
                     command=open_pdf_report).pack(side="right", padx=5)

            # Add Delete Report button
            ttk.Button(button_frame, text="Delete Report",
                     command=delete_pdf_report).pack(side="right", padx=5)

            # Add a separator
            ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=20, pady=5)

def logout():
    """Log out and return to the login screen"""
    if mb.askyesno("Logout", "Are you sure you want to log out?"):
        try:
            # Launch the login.py directly instead of auth_system.py
            # This ensures we use the original login page that's known to work well
            subprocess.Popen([sys.executable, "login.py"])
            # Close the main application
            root.destroy()
        except Exception as e:
            mb.showerror("Error", f"Error returning to login screen: {str(e)}")
            # If all else fails, just exit
            root.destroy()

def window():
    """Exit the application"""
    if mb.askyesno("Exit", "Are you sure you want to exit?"):
        root.destroy()

# Create styled buttons for the workflow
# Button style configuration
button_padding = 10  # Vertical padding between buttons
button_height = 2    # Button height in text units

# 1. Select Image button
select_img_btn = ttk.Button(
    button_frame,
    text="1. Select X-ray Image",
    command=openimage,
    style='TButton'
)
select_img_btn.pack(fill='x', pady=(0, button_padding), ipady=button_height)

# 2. Image Preprocessing button
preprocess_btn = ttk.Button(
    button_frame,
    text="2. Image Preprocessing",
    command=preprocess_image,
    style='TButton'
)
preprocess_btn.pack(fill='x', pady=(0, button_padding), ipady=button_height)

# 3. Analyze Image button
analyze_btn = ttk.Button(
    button_frame,
    text="3. Analyze X-ray",
    command=test_model,
    style='TButton'
)
analyze_btn.pack(fill='x', pady=(0, button_padding), ipady=button_height)

# 4. Generate Report button
report_btn = ttk.Button(
    button_frame,
    text="4. Generate Report",
    command=generate_report,
    style='TButton'
)
report_btn.pack(fill='x', pady=(0, button_padding), ipady=button_height)

# Add a separator
ttk.Separator(control_panel, orient='horizontal').pack(fill='x', pady=10)

# Add additional controls section
additional_label = ttk.Label(control_panel, text="Additional Options", font=('Segoe UI', 12, 'bold'))
additional_label.pack(anchor='w', pady=(10, 15), padx=10)

# Additional buttons frame
additional_frame = ttk.Frame(control_panel)
additional_frame.pack(fill='x', padx=10)

# View Patient History button
view_history_btn = ttk.Button(
    additional_frame,
    text="View Patient History",
    command=lambda: tab_control.select(1),  # Select the Patient History tab
    style='TButton'
)
view_history_btn.pack(fill='x', ipady=button_height, pady=(0, button_padding))

# View Reports button
view_reports_btn = ttk.Button(
    additional_frame,
    text="View Patient Reports",
    command=view_reports,
    style='TButton'
)
view_reports_btn.pack(fill='x', ipady=button_height, pady=(0, button_padding))

# Logout button
logout_btn = ttk.Button(
    additional_frame,
    text="Logout",
    command=logout,
    style='TButton'
)
logout_btn.pack(fill='x', ipady=button_height, pady=(0, button_padding))

# Exit button
exit_btn = ttk.Button(
    additional_frame,
    text="Exit Application",
    command=window,
    style='TButton'
)
exit_btn.pack(fill='x', ipady=button_height, pady=(0, button_padding))

# Initialize the patient history tab
refresh_history_tab()

# Start the application
root.mainloop()
