import os
import json
import hashlib
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageFilter, ImageEnhance
import subprocess
import sys
import sqlite3
from user_database import set_current_user

# Define color scheme (matching the main application)
PRIMARY_COLOR = "#2c3e50"  # Dark blue-gray
SECONDARY_COLOR = "#3498db"  # Bright blue
ACCENT_COLOR = "#e74c3c"  # Red
BG_COLOR = "#ecf0f1"  # Light gray
TEXT_COLOR = "#2c3e50"  # Dark blue-gray
HIGHLIGHT_COLOR = "#2ecc71"  # Green

# User database file for JSON-based authentication
USER_DB_FILE = "users.json"

class Login:
    def __init__(self, root):
        """Initialize the login window"""
        self.root = root
        self.root.title("Scoliosis Detection System - Login")
        self.root.configure(bg=BG_COLOR)

        # Set window to full screen
        self.screen_width = self.root.winfo_screenwidth()
        self.screen_height = self.root.winfo_screenheight()

        # Configure for full screen
        self.root.geometry(f"{self.screen_width}x{self.screen_height}+0+0")
        self.root.resizable(True, True)

        # Start in normal window mode with maximized state
        self.is_fullscreen = False
        self.root.state('zoomed')  # Maximize the window

        # Bind F11 to toggle fullscreen and Escape to exit fullscreen
        self.root.bind("<F11>", self.toggle_fullscreen)
        self.root.bind("<Escape>", self.end_fullscreen)

        # Create window control buttons
        self.create_window_controls()

        # Create styles for widgets
        self.create_styles()

        # Create the main layout
        self.create_layout()

        # Load user database
        self.load_users()

        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())

    def toggle_fullscreen(self, event=None):
        """Toggle between fullscreen and windowed mode"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)
        return "break"  # Prevent default behavior

    def end_fullscreen(self, event=None):
        """Exit fullscreen mode"""
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)
        return "break"  # Prevent default behavior

    def create_styles(self):
        """Create styles for the UI elements"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure basic styles
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('TEntry', font=('Segoe UI', 10))

        # Custom button styles
        self.style.configure('Login.TButton',
                           font=('Segoe UI', 12, 'bold'),
                           background=SECONDARY_COLOR,
                           foreground='white')

        self.style.configure('Register.TButton',
                           font=('Segoe UI', 10),
                           background=BG_COLOR,
                           foreground=TEXT_COLOR)

    def create_layout(self):
        """Create the main layout of the login window"""
        # Create a two-column layout
        self.main_frame = ttk.Frame(self.root, style='TFrame')
        self.main_frame.pack(fill='both', expand=True)

        # Left column - Logo/Image - width is 40% of screen width
        self.left_width = int(self.screen_width * 0.4)
        self.left_frame = ttk.Frame(self.main_frame, style='TFrame', width=self.left_width)
        self.left_frame.pack(side='left', fill='both')
        self.left_frame.pack_propagate(False)  # Maintain width

        # Create decorative background
        self.create_decorative_background()

        # Right column - Login form
        self.right_frame = ttk.Frame(self.main_frame, style='TFrame')
        self.right_frame.pack(side='right', fill='both', expand=True)

        # Login form container (centered)
        self.login_container = ttk.Frame(self.right_frame, style='TFrame')
        self.login_container.place(relx=0.5, rely=0.5, anchor='center')

        # Login form title
        title_label = ttk.Label(self.login_container,
                              text="Welcome Back",
                              font=('Segoe UI', 24, 'bold'),
                              foreground=PRIMARY_COLOR)
        title_label.pack(pady=(0, 5))

        # Login form subtitle
        subtitle_label = ttk.Label(self.login_container,
                                 text="Sign in to continue to the Scoliosis Detection System",
                                 font=('Segoe UI', 10),
                                 foreground=TEXT_COLOR)
        subtitle_label.pack(pady=(0, 30))

        # Username field
        username_frame = ttk.Frame(self.login_container, style='TFrame')
        username_frame.pack(fill='x', pady=5)

        username_label = ttk.Label(username_frame,
                                 text="Username",
                                 font=('Segoe UI', 10, 'bold'),
                                 foreground=TEXT_COLOR)
        username_label.pack(anchor='w', pady=(0, 5))

        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(username_frame,
                                 textvariable=self.username_var,
                                 font=('Segoe UI', 12),
                                 width=30)
        username_entry.pack(ipady=5)
        username_entry.focus_set()  # Set initial focus

        # Password field
        password_frame = ttk.Frame(self.login_container, style='TFrame')
        password_frame.pack(fill='x', pady=15)

        password_label = ttk.Label(password_frame,
                                 text="Password",
                                 font=('Segoe UI', 10, 'bold'),
                                 foreground=TEXT_COLOR)
        password_label.pack(anchor='w', pady=(0, 5))

        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(password_frame,
                                      textvariable=self.password_var,
                                      font=('Segoe UI', 12),
                                      width=30,
                                      show="•")
        self.password_entry.pack(ipady=5)

        # Remember me and forgot password row
        options_frame = ttk.Frame(self.login_container, style='TFrame')
        options_frame.pack(fill='x', pady=10)

        self.remember_var = tk.BooleanVar()
        remember_check = ttk.Checkbutton(options_frame,
                                        text="Remember me",
                                        variable=self.remember_var)
        remember_check.pack(side='left')

        forgot_btn = ttk.Label(options_frame,
                             text="Forgot password?",
                             foreground=SECONDARY_COLOR,
                             cursor="hand2")
        forgot_btn.pack(side='right')
        forgot_btn.bind("<Button-1>", self.forgot_password)

        # Login button
        login_btn = ttk.Button(self.login_container,
                             text="Sign In",
                             command=self.login,
                             style='Login.TButton',
                             width=30)
        login_btn.pack(pady=20, ipady=8)

        # Register link
        register_frame = ttk.Frame(self.login_container, style='TFrame')
        register_frame.pack(pady=10)

        register_label = ttk.Label(register_frame,
                                 text="Don't have an account? ",
                                 foreground=TEXT_COLOR)
        register_label.pack(side='left')

        register_link = ttk.Label(register_frame,
                                text="Register",
                                foreground=SECONDARY_COLOR,
                                cursor="hand2")
        register_link.pack(side='left')
        register_link.bind("<Button-1>", self.show_register)

    def create_decorative_background(self):
        """Create a decorative background for the left frame"""
        try:
            # Try to load the logo if it exists
            if os.path.exists("logo.png"):
                # Create a canvas for the background
                canvas = tk.Canvas(self.left_frame, width=self.left_width, height=self.screen_height,
                                 bg=PRIMARY_COLOR, highlightthickness=0)
                canvas.pack(fill='both', expand=True)

                # Create gradient effect
                for i in range(self.screen_height):
                    # Gradient from PRIMARY_COLOR to a slightly lighter shade
                    r = int(44 + (i/self.screen_height) * 20)  # 2c -> 44 in decimal
                    g = int(62 + (i/self.screen_height) * 20)  # 3e -> 62 in decimal
                    b = int(80 + (i/self.screen_height) * 20)  # 50 -> 80 in decimal
                    color = f'#{r:02x}{g:02x}{b:02x}'
                    canvas.create_line(0, i, self.left_width, i, fill=color)

                # Load and resize the logo - size proportional to screen
                logo_size = int(min(self.screen_width, self.screen_height) * 0.2)  # 20% of smaller dimension
                logo_img = Image.open("logo.png")
                logo_img = logo_img.resize((logo_size, logo_size), Image.LANCZOS)

                # Apply a slight glow effect
                glow_img = logo_img.copy()
                glow_img = glow_img.filter(ImageFilter.GaussianBlur(10))
                enhancer = ImageEnhance.Brightness(glow_img)
                glow_img = enhancer.enhance(1.5)

                # Convert to PhotoImage
                logo_photo = ImageTk.PhotoImage(logo_img)

                # Display the logo - centered in left panel
                logo_x = self.left_width // 2
                logo_y = self.screen_height // 4  # Position at 1/4 of screen height
                canvas.create_image(logo_x, logo_y, image=logo_photo)
                canvas.image = logo_photo  # Keep a reference

                # Add application name - positioned below logo
                title_font_size = int(min(self.screen_width, self.screen_height) * 0.02)  # Scale font size
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height // 2,  # Middle of screen
                    text="Scoliosis Detection System",
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white'
                )

                # Add tagline - positioned below title
                subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)  # Scale font size
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height // 2 + title_font_size + 10,  # Below title
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )
            else:
                # If logo doesn't exist, create a gradient background
                canvas = tk.Canvas(self.left_frame, width=self.left_width, height=self.screen_height,
                                 bg=PRIMARY_COLOR, highlightthickness=0)
                canvas.pack(fill='both', expand=True)

                # Create gradient effect
                for i in range(self.screen_height):
                    # Gradient from PRIMARY_COLOR to a slightly lighter shade
                    r = int(44 + (i/self.screen_height) * 20)
                    g = int(62 + (i/self.screen_height) * 20)
                    b = int(80 + (i/self.screen_height) * 20)
                    color = f'#{r:02x}{g:02x}{b:02x}'
                    canvas.create_line(0, i, self.left_width, i, fill=color)

                # Add application name - positioned in the middle
                title_font_size = int(min(self.screen_width, self.screen_height) * 0.025)
                canvas.create_text(
                    self.left_width // 2,
                    self.screen_height // 3,
                    text="Scoliosis\nDetection\nSystem",
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )

                # Add tagline
                subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)
                canvas.create_text(
                    self.left_width // 2,
                    self.screen_height // 2 + 50,
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )
        except Exception as e:
            print(f"Error creating decorative background: {e}")
            # Fallback to simple background
            left_bg = tk.Frame(self.left_frame, bg=PRIMARY_COLOR)
            left_bg.pack(fill='both', expand=True)

            # Calculate font size based on screen dimensions
            title_font_size = int(min(self.screen_width, self.screen_height) * 0.025)
            subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)

            app_name = tk.Label(left_bg, text="Scoliosis\nDetection\nSystem",
                              font=('Segoe UI', title_font_size, 'bold'),
                              bg=PRIMARY_COLOR, fg='white')
            app_name.pack(pady=(self.screen_height//3, 20))

            # Add tagline
            tagline = tk.Label(left_bg, text="Advanced X-ray Analysis & Reporting",
                             font=('Segoe UI', subtitle_font_size),
                             bg=PRIMARY_COLOR, fg='white')
            tagline.pack()

    def load_users(self):
        """Load user data from JSON file"""
        self.users = {}

        # First try to load from JSON file
        if os.path.exists(USER_DB_FILE):
            try:
                with open(USER_DB_FILE, 'r') as f:
                    self.users = json.load(f)
            except Exception as e:
                print(f"Error loading user database from JSON: {e}")

        # If no users found in JSON, try to load from SQLite
        if not self.users:
            try:
                # Connect to SQLite database
                conn = sqlite3.connect('evaluation.db')
                cursor = conn.cursor()

                # Create tables if they don't exist (for backward compatibility)
                cursor.execute("CREATE TABLE IF NOT EXISTS admin_registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")
                cursor.execute("CREATE TABLE IF NOT EXISTS registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")

                # Get users from admin_registration table
                cursor.execute("SELECT Fullname, username, password FROM admin_registration")
                admin_users = cursor.fetchall()

                # Get users from registration table
                cursor.execute("SELECT Fullname, username, password FROM registration")
                regular_users = cursor.fetchall()

                # Convert SQLite users to JSON format
                for fullname, username, password in admin_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "admin"
                        }

                for fullname, username, password in regular_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "user"
                        }

                # Save the converted users to JSON
                if self.users:
                    self.save_users()

                conn.close()
            except Exception as e:
                print(f"Error loading users from SQLite: {e}")

        # If still no users, create default admin
        if not self.users:
            # Create default admin user if no users exist
            admin_password = self.hash_password("admin123")
            self.users = {
                "admin": {
                    "password": admin_password,
                    "name": "Administrator",
                    "role": "admin"
                }
            }
            self.save_users()

    def save_users(self):
        """Save user data to JSON file"""
        try:
            with open(USER_DB_FILE, 'w') as f:
                json.dump(self.users, f, indent=4)
        except Exception as e:
            print(f"Error saving user database: {e}")

    def hash_password(self, password):
        """Hash a password using SHA-256"""
        if not password:
            return ""
        return hashlib.sha256(str(password).encode()).hexdigest()

    def login(self):
        """Handle login button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get()

        if not username or not password:
            messagebox.showerror("Login Error", "Please enter both username and password.")
            return

        # First try JSON-based authentication
        if username in self.users:
            stored_password = self.users[username]["password"]
            if self.hash_password(password) == stored_password:
                # Login successful
                messagebox.showinfo("Login Successful", f"Welcome, {self.users[username]['name']}!")

                # Set the current user in the user_database module
                set_current_user(username)

                self.launch_main_application(username)
                return

        # If JSON auth fails, try SQLite (for backward compatibility)
        try:
            # Connect to SQLite database
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Try to find user in registration table
            cursor.execute('SELECT * FROM registration WHERE username = ? and password = ?',
                         (username, password))
            result = cursor.fetchall()

            # If not found, try admin_registration table
            if not result:
                cursor.execute('SELECT * FROM admin_registration WHERE username = ? and password = ?',
                             (username, password))
                result = cursor.fetchall()

            conn.close()

            if result:
                # Login successful with SQLite
                messagebox.showinfo("Login Successful", "Welcome to the Scoliosis Detection System!")

                # Add user to JSON database for future logins
                self.users[username] = {
                    "password": self.hash_password(password),
                    "name": result[0][0] if result[0][0] else username,  # Use Fullname if available
                    "role": "admin" if result[0][0] == "admin_registration" else "user"
                }
                self.save_users()

                # Set the current user in the user_database module
                set_current_user(username)

                self.launch_main_application(username)
                return
        except Exception as e:
            print(f"SQLite authentication error: {e}")

        # If all authentication methods fail
        messagebox.showerror("Login Error", "Invalid username or password.")

    def launch_main_application(self, username):
        """Launch the main application after successful login"""
        try:
            # Launch the main application with the username as a command-line argument
            subprocess.Popen([sys.executable, "GUI_Basic.py", username])
            # Hide the login window after launching the main app
            self.root.withdraw()  # This hides the window without destroying it
        except Exception as e:
            messagebox.showerror("Error", f"Error launching application: {str(e)}")

    def create_window_controls(self):
        """Create window control buttons (minimize, maximize, close)"""
        # Create a frame for the window controls
        control_frame = tk.Frame(self.root, bg=PRIMARY_COLOR)
        control_frame.place(x=self.screen_width-110, y=10, width=100, height=30)

        # Minimize button
        minimize_btn = tk.Button(control_frame, text="—", bg=PRIMARY_COLOR, fg="white",
                               font=("Arial", 12), bd=0, padx=5,
                               command=self.minimize_window)
        minimize_btn.pack(side="left", padx=2)

        # Maximize/Restore button
        self.max_restore_btn = tk.Button(control_frame, text="□", bg=PRIMARY_COLOR, fg="white",
                                       font=("Arial", 12), bd=0, padx=5,
                                       command=self.toggle_maximize)
        self.max_restore_btn.pack(side="left", padx=2)

        # Close button
        close_btn = tk.Button(control_frame, text="×", bg=PRIMARY_COLOR, fg="white",
                            font=("Arial", 12), bd=0, padx=5,
                            command=self.close_window)
        close_btn.pack(side="left", padx=2)

        # Add hover effects
        for btn in [minimize_btn, self.max_restore_btn, close_btn]:
            btn.bind("<Enter>", lambda e, b=btn: b.config(bg=SECONDARY_COLOR))
            btn.bind("<Leave>", lambda e, b=btn: b.config(bg=PRIMARY_COLOR))

    def minimize_window(self):
        """Minimize the window"""
        self.root.iconify()

    def toggle_maximize(self):
        """Toggle between maximized and normal window state"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
            self.max_restore_btn.config(text="□")
        else:
            self.root.state('zoomed')
            self.max_restore_btn.config(text="❐")

    def close_window(self):
        """Close the window"""
        self.root.destroy()

    def forgot_password(self, event=None):
        """Handle forgot password link click"""
        username = self.username_var.get().strip()

        if not username:
            messagebox.showerror("Error", "Please enter your username first.")
            return

        if username in self.users:
            # In a real application, this would send a password reset email
            # For this demo, we'll just reset to a default password
            new_password = "password123"
            self.users[username]["password"] = self.hash_password(new_password)
            self.save_users()

            messagebox.showinfo("Password Reset",
                              f"Password has been reset to: {new_password}\n\n"
                              "Please change it after logging in.")
        else:
            messagebox.showerror("Error", "Username not found.")

    def show_register(self, event=None):
        """Show the registration form"""
        try:
            # Launch the registration application without destroying the login window
            subprocess.Popen([sys.executable, "registration_new.py"])
            # Minimize the login window instead of destroying it
            self.root.iconify()
        except Exception as e:
            messagebox.showerror("Error", f"Error launching registration screen: {str(e)}")


def main():
    """Main function to run the login system"""
    root = tk.Tk()

    # Set the application icon if available
    try:
        if os.path.exists("logo.png"):
            icon = tk.PhotoImage(file="logo.png")
            root.iconphoto(True, icon)
    except Exception as e:
        print(f"Error setting application icon: {e}")

    app = Login(root)
    root.mainloop()


if __name__ == "__main__":
    main()
