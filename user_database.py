import os
import sqlite3
import hashlib
import time
import datetime
import json

# Global variable to store current user information
current_user = {
    "user_id": None,
    "username": None,
    "name": "Guest",
    "role": "user",
    "is_authenticated": False
}

# User database file for JSON-based authentication
USER_DB_FILE = "users.json"

def initialize_user_database():
    """Initialize the user database and add necessary columns to existing tables"""
    # First, ensure the evaluation.db exists
    conn = sqlite3.connect('evaluation.db')
    cursor = conn.cursor()

    print("Initializing user database...")

    # Create patients table if it doesn't exist (with user_id column)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            created_date TEXT NOT NULL,
            last_visit TEXT NOT NULL,
            user_id TEXT
        )
    ''')

    # Create examinations table if it doesn't exist (with user_id column)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS examinations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date TEXT NOT NULL,
            diagnosis TEXT NOT NULL,
            confidence REAL,
            image_path TEXT,
            report_path TEXT,
            html_report_path TEXT,
            notes TEXT,
            user_id TEXT,
            FOREIGN KEY (patient_id) REFERENCES patients (id)
        )
    ''')

    # Create reports table if it doesn't exist (with user_id column)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reports (
            patient_id TEXT,
            patient_name TEXT,
            timestamp TEXT,
            diagnosis TEXT,
            image_path TEXT,
            report_path TEXT,
            user_id TEXT
        )
    ''')

    print("Database tables initialized with user_id columns")

    conn.commit()
    conn.close()

    print("User database initialization complete")

def set_current_user(username):
    """Set the current user based on username"""
    global current_user

    # Reset the current user first
    current_user = {
        "user_id": None,
        "username": None,
        "name": "Guest",
        "role": "user",
        "is_authenticated": False
    }

    # Reset the database connection in patient_database.py
    try:
        from patient_database import get_db_instance
        # Get the current instance and close it
        db = get_db_instance()
        db.close()
        # Reset the singleton instance
        import patient_database
        patient_database._instance = None
        print("Reset database connection")
    except Exception as e:
        print(f"Error resetting database connection: {e}")

    # Load users from JSON file
    if os.path.exists(USER_DB_FILE):
        try:
            with open(USER_DB_FILE, "r") as f:
                users = json.load(f)

                if username in users:
                    # Set current user information
                    current_user["username"] = username
                    current_user["name"] = users[username].get("name", username)
                    current_user["role"] = users[username].get("role", "user")
                    current_user["user_id"] = username  # Use username as user_id for simplicity
                    current_user["is_authenticated"] = True

                    # Print debug information
                    print(f"User authenticated: {username} (ID: {username})")

                    # Ensure the database has the user_id column
                    initialize_user_database()

                    return True
        except Exception as e:
            print(f"Error loading user data: {e}")

    # If we get here, authentication failed
    print(f"Authentication failed for username: {username}")
    return False

def get_current_user():
    """Get the current user information"""
    return current_user

def is_authenticated():
    """Check if a user is currently authenticated"""
    return current_user["is_authenticated"]

def get_current_username():
    """Get the current username"""
    return current_user["username"] if current_user["is_authenticated"] else None

def get_current_user_id():
    """Get the current user ID"""
    return current_user["user_id"] if current_user["is_authenticated"] else None

def filter_query_by_user(query, params=None):
    """Add user filtering to a database query if a user is authenticated"""
    if not params:
        params = []

    if is_authenticated():
        # If query already has a WHERE clause, add AND user_id = ?
        if "WHERE" in query.upper():
            query += " AND user_id = ?"
        else:
            query += " WHERE user_id = ?"

        params.append(get_current_user_id())

    return query, params

def associate_with_current_user(table, id_column, id_value):
    """Associate an existing record with the current user"""
    if not is_authenticated():
        return False

    conn = sqlite3.connect('evaluation.db')
    cursor = conn.cursor()

    try:
        cursor.execute(f"UPDATE {table} SET user_id = ? WHERE {id_column} = ?",
                      (get_current_user_id(), id_value))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error associating record with user: {e}")
        conn.close()
        return False

# Initialize the database when the module is imported
initialize_user_database()
