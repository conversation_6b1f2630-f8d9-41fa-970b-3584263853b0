import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from PIL import Image, ImageTk
import datetime
from patient_database import get_db_instance

# Define color scheme
PRIMARY_COLOR = "#2c3e50"  # Dark blue-gray
SECONDARY_COLOR = "#3498db"  # Bright blue
ACCENT_COLOR = "#e74c3c"  # Red
BG_COLOR = "#ecf0f1"  # Light gray
TEXT_COLOR = "#2c3e50"  # Dark blue-gray
HIGHLIGHT_COLOR = "#2ecc71"  # Green

class PatientHistoryViewer:
    def __init__(self, root, patient_id=None, patient_name=None):
        """Initialize the patient history viewer"""
        self.root = root
        self.db = get_db_instance()
        self.patient_id = patient_id
        self.patient_name = patient_name
        
        # If patient ID is provided, load that patient
        # Otherwise, show a patient selection screen
        if patient_id:
            self.setup_ui()
            self.load_patient(patient_id)
        else:
            self.setup_selection_ui()
    
    def setup_ui(self):
        """Set up the main UI for viewing patient history"""
        self.root.title("Patient History Viewer")
        self.root.configure(bg=BG_COLOR)
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Set window size (80% of screen)
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        self.root.geometry(f"{window_width}x{window_height}")
        
        # Create styles
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('Header.TLabel', font=('Segoe UI', 16, 'bold'), background=PRIMARY_COLOR, foreground='white')
        self.style.configure('Subheader.TLabel', font=('Segoe UI', 12, 'bold'), background=BG_COLOR, foreground=TEXT_COLOR)
        
        # Create header
        self.header_frame = ttk.Frame(self.root, style='TFrame')
        self.header_frame.pack(fill='x', padx=10, pady=10)
        
        # Back button
        self.back_btn = ttk.Button(self.header_frame, text="← Back", command=self.go_back)
        self.back_btn.pack(side='left', padx=5, pady=5)
        
        # Patient info
        self.patient_info_frame = ttk.Frame(self.header_frame, style='TFrame')
        self.patient_info_frame.pack(side='left', padx=20)
        
        self.patient_name_label = ttk.Label(self.patient_info_frame, text="", font=('Segoe UI', 16, 'bold'))
        self.patient_name_label.pack(anchor='w')
        
        self.patient_id_label = ttk.Label(self.patient_info_frame, text="")
        self.patient_id_label.pack(anchor='w')
        
        # Main content area - split into two panes
        self.content_frame = ttk.Frame(self.root, style='TFrame')
        self.content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create a PanedWindow
        self.paned_window = ttk.PanedWindow(self.content_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill='both', expand=True)
        
        # Left pane - Examination list
        self.exam_frame = ttk.Frame(self.paned_window, style='TFrame')
        self.paned_window.add(self.exam_frame, weight=1)
        
        # Examination list header
        ttk.Label(self.exam_frame, text="Examination History", style='Subheader.TLabel').pack(anchor='w', padx=5, pady=5)
        
        # Create a treeview for examinations
        self.exam_tree = ttk.Treeview(self.exam_frame, columns=('date', 'diagnosis'), show='headings')
        self.exam_tree.heading('date', text='Date')
        self.exam_tree.heading('diagnosis', text='Diagnosis')
        self.exam_tree.column('date', width=150)
        self.exam_tree.column('diagnosis', width=200)
        self.exam_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add scrollbar to treeview
        scrollbar = ttk.Scrollbar(self.exam_tree, orient="vertical", command=self.exam_tree.yview)
        scrollbar.pack(side='right', fill='y')
        self.exam_tree.configure(yscrollcommand=scrollbar.set)
        
        # Bind selection event
        self.exam_tree.bind('<<TreeviewSelect>>', self.on_exam_selected)
        
        # Right pane - Examination details
        self.detail_frame = ttk.Frame(self.paned_window, style='TFrame')
        self.paned_window.add(self.detail_frame, weight=2)
        
        # Examination details header
        ttk.Label(self.detail_frame, text="Examination Details", style='Subheader.TLabel').pack(anchor='w', padx=5, pady=5)
        
        # Create a frame for examination details
        self.detail_content = ttk.Frame(self.detail_frame, style='TFrame')
        self.detail_content.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Placeholder for when no examination is selected
        self.placeholder_label = ttk.Label(self.detail_content, text="Select an examination to view details")
        self.placeholder_label.pack(pady=50)
    
    def setup_selection_ui(self):
        """Set up the UI for selecting a patient"""
        self.root.title("Patient Selection")
        self.root.configure(bg=BG_COLOR)
        
        # Set window size
        self.root.geometry("600x500")
        
        # Create styles
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('Header.TLabel', font=('Segoe UI', 16, 'bold'), background=PRIMARY_COLOR, foreground='white')
        
        # Create header
        header_frame = ttk.Frame(self.root, style='TFrame')
        header_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(header_frame, text="Select a Patient", style='Header.TLabel').pack(anchor='w')
        
        # Search frame
        search_frame = ttk.Frame(self.root, style='TFrame')
        search_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(search_frame, text="Search by name:").pack(side='left', padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left', padx=5)
        
        search_btn = ttk.Button(search_frame, text="Search", command=self.search_patients)
        search_btn.pack(side='left', padx=5)
        
        # Patient list frame
        list_frame = ttk.Frame(self.root, style='TFrame')
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create a treeview for patients
        self.patient_tree = ttk.Treeview(list_frame, columns=('id', 'name', 'last_visit'), show='headings')
        self.patient_tree.heading('id', text='ID')
        self.patient_tree.heading('name', text='Name')
        self.patient_tree.heading('last_visit', text='Last Visit')
        self.patient_tree.column('id', width=100)
        self.patient_tree.column('name', width=200)
        self.patient_tree.column('last_visit', width=150)
        self.patient_tree.pack(fill='both', expand=True)
        
        # Add scrollbar to treeview
        scrollbar = ttk.Scrollbar(self.patient_tree, orient="vertical", command=self.patient_tree.yview)
        scrollbar.pack(side='right', fill='y')
        self.patient_tree.configure(yscrollcommand=scrollbar.set)
        
        # Bind double-click event
        self.patient_tree.bind('<Double-1>', self.on_patient_selected)
        
        # Button frame
        button_frame = ttk.Frame(self.root, style='TFrame')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        select_btn = ttk.Button(button_frame, text="View Selected Patient", command=self.view_selected_patient)
        select_btn.pack(side='right', padx=5)
        
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.root.destroy)
        cancel_btn.pack(side='right', padx=5)
        
        # Load all patients initially
        self.load_all_patients()
    
    def load_all_patients(self):
        """Load all patients into the treeview"""
        # Clear existing items
        for item in self.patient_tree.get_children():
            self.patient_tree.delete(item)
        
        # Get all patients from the database
        patients = self.db.get_all_patients()
        
        # Add patients to the treeview
        for patient in patients:
            self.patient_tree.insert('', 'end', values=(
                patient['id'],
                patient['name'],
                self.format_date(patient['last_visit'])
            ))
    
    def search_patients(self):
        """Search for patients by name"""
        search_term = self.search_var.get().strip()
        
        # Clear existing items
        for item in self.patient_tree.get_children():
            self.patient_tree.delete(item)
        
        if search_term:
            # Search for patients by name
            patients = self.db.get_patient_by_name(search_term)
        else:
            # If search term is empty, show all patients
            patients = self.db.get_all_patients()
        
        # Add patients to the treeview
        for patient in patients:
            self.patient_tree.insert('', 'end', values=(
                patient['id'],
                patient['name'],
                self.format_date(patient['last_visit'])
            ))
    
    def on_patient_selected(self, event):
        """Handle double-click on a patient"""
        self.view_selected_patient()
    
    def view_selected_patient(self):
        """View the selected patient's history"""
        selected_items = self.patient_tree.selection()
        if not selected_items:
            messagebox.showinfo("Selection Required", "Please select a patient to view.")
            return
        
        # Get the selected patient's ID and name
        item = selected_items[0]
        patient_id = self.patient_tree.item(item, 'values')[0]
        patient_name = self.patient_tree.item(item, 'values')[1]
        
        # Clear the current UI
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Set up the main UI
        self.patient_id = patient_id
        self.patient_name = patient_name
        self.setup_ui()
        self.load_patient(patient_id)
    
    def load_patient(self, patient_id):
        """Load patient data and examinations"""
        # Get patient information
        patient = self.db.get_patient(patient_id)
        if not patient:
            messagebox.showerror("Error", f"Patient with ID {patient_id} not found.")
            self.go_back()
            return
        
        # Update patient info labels
        self.patient_name_label.config(text=patient['name'])
        self.patient_id_label.config(text=f"Patient ID: {patient['id']}")
        
        # Get patient examinations
        self.load_examinations(patient_id)
    
    def load_examinations(self, patient_id):
        """Load examinations for the patient"""
        # Clear existing items
        for item in self.exam_tree.get_children():
            self.exam_tree.delete(item)
        
        # Get examinations from the database
        examinations = self.db.get_patient_examinations(patient_id)
        
        # Add examinations to the treeview
        for exam in examinations:
            self.exam_tree.insert('', 'end', values=(
                self.format_date(exam['exam_date']),
                exam['diagnosis']
            ), tags=(str(exam['id']),))
    
    def on_exam_selected(self, event):
        """Handle selection of an examination"""
        selected_items = self.exam_tree.selection()
        if not selected_items:
            return
        
        # Get the selected examination's ID
        item = selected_items[0]
        exam_id = int(self.exam_tree.item(item, 'tags')[0])
        
        # Load examination details
        self.load_examination_details(exam_id)
    
    def load_examination_details(self, exam_id):
        """Load and display examination details"""
        # Get examination from the database
        exam = self.db.get_examination(exam_id)
        if not exam:
            messagebox.showerror("Error", f"Examination with ID {exam_id} not found.")
            return
        
        # Clear the detail content frame
        for widget in self.detail_content.winfo_children():
            widget.destroy()
        
        # Create a canvas with scrollbar for the details
        canvas = tk.Canvas(self.detail_content, bg=BG_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.detail_content, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='TFrame')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Add examination details to the scrollable frame
        # Date and time
        date_frame = ttk.Frame(scrollable_frame, style='TFrame')
        date_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(date_frame, text="Date:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(date_frame, text=self.format_date(exam['exam_date'])).pack(side='left', padx=5)
        
        # Diagnosis
        diag_frame = ttk.Frame(scrollable_frame, style='TFrame')
        diag_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(diag_frame, text="Diagnosis:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(diag_frame, text=exam['diagnosis']).pack(side='left', padx=5)
        
        # Confidence score
        if exam['confidence']:
            conf_frame = ttk.Frame(scrollable_frame, style='TFrame')
            conf_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(conf_frame, text="Confidence Score:", font=('Segoe UI', 10, 'bold')).pack(side='left')
            ttk.Label(conf_frame, text=f"{exam['confidence']:.2f}%").pack(side='left', padx=5)
        
        # Add separator
        ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=10, pady=10)
        
        # X-ray image
        if exam['image_path'] and os.path.exists(exam['image_path']):
            img_frame = ttk.Frame(scrollable_frame, style='TFrame')
            img_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(img_frame, text="X-ray Image:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')
            
            try:
                # Load and resize the image
                img = Image.open(exam['image_path'])
                img.thumbnail((400, 400))  # Resize to fit
                photo = ImageTk.PhotoImage(img)
                
                # Display the image
                img_label = ttk.Label(img_frame, image=photo)
                img_label.image = photo  # Keep a reference
                img_label.pack(pady=5)
                
                # Add a button to view the full image
                view_btn = ttk.Button(img_frame, text="View Full Image", 
                                     command=lambda: self.view_full_image(exam['image_path']))
                view_btn.pack(pady=5)
            except Exception as e:
                ttk.Label(img_frame, text=f"Error loading image: {str(e)}").pack(pady=5)
        
        # Report links
        if exam['report_path'] or exam['html_report_path']:
            report_frame = ttk.Frame(scrollable_frame, style='TFrame')
            report_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(report_frame, text="Reports:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')
            
            if exam['report_path'] and os.path.exists(exam['report_path']):
                pdf_btn = ttk.Button(report_frame, text="View PDF Report", 
                                    command=lambda: self.open_file(exam['report_path']))
                pdf_btn.pack(anchor='w', pady=2)
            
            if exam['html_report_path'] and os.path.exists(exam['html_report_path']):
                html_btn = ttk.Button(report_frame, text="View HTML Report", 
                                     command=lambda: self.open_file(exam['html_report_path']))
                html_btn.pack(anchor='w', pady=2)
        
        # Notes section
        notes_frame = ttk.Frame(scrollable_frame, style='TFrame')
        notes_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(notes_frame, text="Notes:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')
        
        # Create a text widget for notes with a save button
        self.notes_text = tk.Text(notes_frame, height=5, width=40, wrap='word')
        self.notes_text.pack(fill='x', pady=5)
        
        if exam['notes']:
            self.notes_text.insert('1.0', exam['notes'])
        
        # Save notes button
        save_btn = ttk.Button(notes_frame, text="Save Notes", 
                             command=lambda: self.save_notes(exam_id))
        save_btn.pack(anchor='e', pady=5)
    
    def save_notes(self, exam_id):
        """Save the notes for an examination"""
        notes = self.notes_text.get('1.0', 'end-1c')  # Get text without the final newline
        
        if self.db.update_examination_notes(exam_id, notes):
            messagebox.showinfo("Success", "Notes saved successfully.")
        else:
            messagebox.showerror("Error", "Failed to save notes.")
    
    def view_full_image(self, image_path):
        """Open the image in a new window at full size"""
        if not os.path.exists(image_path):
            messagebox.showerror("Error", "Image file not found.")
            return
        
        # Create a new top-level window
        img_window = tk.Toplevel(self.root)
        img_window.title("X-ray Image")
        
        try:
            # Load the image
            img = Image.open(image_path)
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # Calculate maximum size while maintaining aspect ratio
            max_width = screen_width * 0.8
            max_height = screen_height * 0.8
            
            width, height = img.size
            scale = min(max_width / width, max_height / height)
            
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # Resize the image
            img = img.resize((new_width, new_height), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)
            
            # Display the image
            img_label = ttk.Label(img_window, image=photo)
            img_label.image = photo  # Keep a reference
            img_label.pack(padx=10, pady=10)
            
            # Add a close button
            close_btn = ttk.Button(img_window, text="Close", command=img_window.destroy)
            close_btn.pack(pady=10)
            
            # Center the window
            img_window.update_idletasks()
            x = (screen_width - img_window.winfo_width()) // 2
            y = (screen_height - img_window.winfo_height()) // 2
            img_window.geometry(f"+{x}+{y}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error displaying image: {str(e)}")
            img_window.destroy()
    
    def open_file(self, file_path):
        """Open a file with the default application"""
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "File not found.")
            return
        
        try:
            import subprocess
            import platform
            
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            else:  # Linux
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            messagebox.showerror("Error", f"Error opening file: {str(e)}")
    
    def format_date(self, date_str):
        """Format a date string for display"""
        try:
            dt = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            return dt.strftime("%b %d, %Y %I:%M %p")
        except:
            return date_str
    
    def go_back(self):
        """Go back to the patient selection screen"""
        # Clear the current UI
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Set up the selection UI
        self.setup_selection_ui()


def main():
    """Main function to run the patient history viewer"""
    root = tk.Tk()
    
    # Check if patient ID was provided as a command-line argument
    patient_id = None
    patient_name = None
    
    if len(sys.argv) > 1:
        patient_id = sys.argv[1]
        if len(sys.argv) > 2:
            patient_name = sys.argv[2]
    
    app = PatientHistoryViewer(root, patient_id, patient_name)
    root.mainloop()


if __name__ == "__main__":
    main()
