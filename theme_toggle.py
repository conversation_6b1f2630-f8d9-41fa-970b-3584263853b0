"""
Theme Toggle Widget
This module provides a toggle button for switching between light and dark themes
"""

import tkinter as tk
from theme_manager import theme_manager

class ThemeToggle(tk.Frame):
    """A toggle button for switching between light and dark themes"""
    
    def __init__(self, parent, callback=None, **kwargs):
        """Initialize the theme toggle button
        
        Args:
            parent: Parent widget
            callback: Optional callback function to call when theme changes
            **kwargs: Additional arguments to pass to the Frame constructor
        """
        # Set default background color
        if 'bg' not in kwargs and 'background' not in kwargs:
            kwargs['bg'] = theme_manager.get_color("frame_bg")
        
        # Initialize the frame
        super().__init__(parent, **kwargs)
        
        # Store the callback
        self.callback = callback
        
        # Create the toggle button
        self._create_toggle()
        
        # Register with theme manager
        theme_manager.register_callback(self._update_appearance)
    
    def _create_toggle(self):
        """Create the toggle button"""
        # Create a frame for the toggle
        self.toggle_frame = tk.Frame(self, bg=theme_manager.get_color("frame_bg"))
        self.toggle_frame.pack(padx=5, pady=5)
        
        # Create the toggle button
        self.is_dark = theme_manager.get_theme() == "dark"
        
        # Create the toggle track (background)
        self.toggle_track = tk.Canvas(
            self.toggle_frame,
            width=40,
            height=20,
            bg=theme_manager.get_color("primary_color") if self.is_dark else "#cccccc",
            highlightthickness=0,
            relief="flat"
        )
        self.toggle_track.pack(side=tk.LEFT, padx=(0, 5))
        
        # Create the rounded track
        self.track = self.toggle_track.create_rounded_rect(
            2, 2, 38, 18, 9,
            fill=theme_manager.get_color("primary_color") if self.is_dark else "#cccccc",
            outline=""
        )
        
        # Create the toggle handle
        self.handle = self.toggle_track.create_oval(
            22 if self.is_dark else 2, 2,
            38 if self.is_dark else 18, 18,
            fill=theme_manager.get_color("highlight_color") if self.is_dark else "#ffffff",
            outline=""
        )
        
        # Create the icon labels
        self.sun_label = tk.Label(
            self.toggle_frame,
            text="☀️",
            font=("Segoe UI", 12),
            bg=theme_manager.get_color("frame_bg"),
            fg=theme_manager.get_color("text_color")
        )
        self.sun_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.moon_label = tk.Label(
            self.toggle_frame,
            text="🌙",
            font=("Segoe UI", 12),
            bg=theme_manager.get_color("frame_bg"),
            fg=theme_manager.get_color("text_color")
        )
        self.moon_label.pack(side=tk.LEFT)
        
        # Bind click events
        self.toggle_track.bind("<Button-1>", self._toggle_theme)
        self.sun_label.bind("<Button-1>", lambda e: self._set_theme("light"))
        self.moon_label.bind("<Button-1>", lambda e: self._set_theme("dark"))
    
    def _toggle_theme(self, event=None):
        """Toggle between light and dark themes"""
        # Toggle the theme
        new_theme = theme_manager.toggle_theme()
        self.is_dark = new_theme == "dark"
        
        # Update the toggle appearance
        self._update_toggle()
        
        # Call the callback if provided
        if self.callback:
            self.callback()
    
    def _set_theme(self, theme):
        """Set the theme to a specific value"""
        # Set the theme
        theme_manager.set_theme(theme)
        self.is_dark = theme == "dark"
        
        # Update the toggle appearance
        self._update_toggle()
        
        # Call the callback if provided
        if self.callback:
            self.callback()
    
    def _update_toggle(self):
        """Update the toggle appearance based on the current theme"""
        # Update the track color
        self.toggle_track.itemconfig(
            self.track,
            fill=theme_manager.get_color("primary_color") if self.is_dark else "#cccccc"
        )
        
        # Update the handle position and color
        self.toggle_track.coords(
            self.handle,
            22 if self.is_dark else 2, 2,
            38 if self.is_dark else 18, 18
        )
        self.toggle_track.itemconfig(
            self.handle,
            fill=theme_manager.get_color("highlight_color") if self.is_dark else "#ffffff"
        )
    
    def _update_appearance(self):
        """Update the appearance of the toggle based on the current theme"""
        # Update the frame background
        self.configure(bg=theme_manager.get_color("frame_bg"))
        self.toggle_frame.configure(bg=theme_manager.get_color("frame_bg"))
        
        # Update the labels
        self.sun_label.configure(
            bg=theme_manager.get_color("frame_bg"),
            fg=theme_manager.get_color("text_color")
        )
        self.moon_label.configure(
            bg=theme_manager.get_color("frame_bg"),
            fg=theme_manager.get_color("text_color")
        )
        
        # Update the toggle
        self._update_toggle()

# Add a method to Canvas to create rounded rectangles
def _create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
    """Create a rounded rectangle on a Canvas widget"""
    points = [
        x1 + radius, y1,
        x2 - radius, y1,
        x2, y1,
        x2, y1 + radius,
        x2, y2 - radius,
        x2, y2,
        x2 - radius, y2,
        x1 + radius, y2,
        x1, y2,
        x1, y2 - radius,
        x1, y1 + radius,
        x1, y1
    ]
    return self.create_polygon(points, **kwargs, smooth=True)

# Add the method to the Canvas class
tk.Canvas.create_rounded_rect = _create_rounded_rect
