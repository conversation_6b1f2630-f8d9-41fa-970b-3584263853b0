
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Scoliosis Assessment Report - Arbaz</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

                    :root {
                        --primary-color: #0277bd;
                        --secondary-color: #58a5f0;
                        --accent-color: #004c8c;
                        --text-color: #333;
                        --light-bg: #f5f9ff;
                        --warning-color: #f57c00;
                        --success-color: #2e7d32;
                        --border-radius: 8px;
                    }

                    * {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                    }

                    body {
                        font-family: 'Roboto', Arial, sans-serif;
                        line-height: 1.6;
                        color: var(--text-color);
                        max-width: 1000px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #fff;
                    }

                    .report-container {
                        border: 1px solid #ddd;
                        border-radius: var(--border-radius);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        overflow: hidden;
                    }

                    .header {
                        background-color: var(--primary-color);
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }

                    .header h1 {
                        font-size: 28px;
                        margin-bottom: 5px;
                    }

                    .header p {
                        font-size: 14px;
                        opacity: 0.9;
                    }

                    .report-body {
                        padding: 30px;
                    }

                    .section {
                        margin-bottom: 30px;
                    }

                    .section-title {
                        color: var(--primary-color);
                        border-bottom: 2px solid var(--secondary-color);
                        padding-bottom: 8px;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                    }

                    .section-title i {
                        margin-right: 10px;
                        font-size: 20px;
                    }

                    .patient-info {
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        display: flex;
                        flex-wrap: wrap;
                    }

                    .patient-info-col {
                        flex: 1;
                        min-width: 250px;
                    }

                    .info-item {
                        margin-bottom: 10px;
                    }

                    .info-item strong {
                        display: inline-block;
                        width: 120px;
                        color: var(--accent-color);
                    }

                    .diagnosis-box {
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--primary-color);
                    }

                    .diagnosis-summary {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 15px;
                    }

                    .diagnosis-text {
                        flex: 2;
                    }

                    .confidence-meter {
                        flex: 1;
                        text-align: right;
                    }

                    .confidence-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: var(--primary-color);
                    }

                    .severity-indicator {
                        display: inline-block;
                        padding: 5px 10px;
                        border-radius: 15px;
                        font-weight: bold;
                        margin-top: 10px;
                        background-color: #81c784;
                        color: white;
                    }

                    .severity-moderate {
                        background-color: #ffb74d;
                    }

                    .severity-severe {
                        background-color: #e57373;
                    }

                    .cobb-angle {
                        display: flex;
                        align-items: center;
                        margin-top: 15px;
                    }

                    .cobb-angle-value {
                        font-size: 22px;
                        font-weight: bold;
                        color: var(--accent-color);
                        margin-right: 10px;
                    }

                    .spine-visualization {
                        display: flex;
                        justify-content: center;
                        margin: 20px 0;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: var(--border-radius);
                    }

                    .spine {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        position: relative;
                        height: 300px;
                        width: 100px;
                    }

                    .vertebra {
                        width: 20px;
                        height: 20px;
                        background-color: var(--primary-color);
                        border-radius: 50%;
                        margin: 5px 0;
                        position: absolute;
                    }

                    .spine-label {
                        position: absolute;
                        left: -30px;
                        font-size: 10px;
                        color: var(--accent-color);
                    }

                    .angle-marker {
                        position: absolute;
                        color: red;
                        font-weight: bold;
                        font-size: 14px;
                    }

                    .image-section {
                        margin-top: 20px;
                        text-align: center;
                    }

                    .image-container {
                        background-color: #000; /* Dark background for X-ray contrast */
                        padding: 20px;
                        border-radius: var(--border-radius);
                        margin: 0 auto;
                        max-width: 90%;
                    }

                    .image-container img {
                        width: 100%;
                        max-height: 700px; /* Increased height for better visibility */
                        object-fit: contain;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                    }

                    /* Lightbox effect for image */
                    .image-container img:hover {
                        cursor: pointer;
                        opacity: 0.9;
                    }

                    .image-caption {
                        margin-top: 15px;
                        font-size: 14px;
                        color: #fff;
                        text-align: center;
                        font-style: italic;
                    }

                    .recommendations {
                        background-color: var(--light-bg);
                        padding: 20px;
                        border-radius: var(--border-radius);
                        border-left: 4px solid var(--warning-color);
                    }

                    .recommendations ul {
                        padding-left: 20px;
                    }

                    .recommendations li {
                        margin-bottom: 10px;
                        position: relative;
                        padding-left: 5px;
                    }

                    .recommendations li::before {
                        content: "•";
                        color: var(--warning-color);
                        font-weight: bold;
                        display: inline-block;
                        width: 1em;
                        margin-left: -1em;
                    }

                    .download-section {
                        margin-top: 30px;
                        text-align: center;
                        padding: 20px;
                        background-color: var(--light-bg);
                        border-radius: var(--border-radius);
                    }

                    .btn {
                        display: inline-block;
                        padding: 10px 20px;
                        margin: 10px;
                        border-radius: 4px;
                        text-decoration: none;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background-color 0.3s;
                        border: none;
                    }

                    .btn-primary {
                        background-color: var(--primary-color);
                        color: white;
                    }

                    .btn-primary:hover {
                        background-color: var(--accent-color);
                    }

                    .btn-secondary {
                        background-color: var(--secondary-color);
                        color: white;
                    }

                    .btn-secondary:hover {
                        background-color: #4294d0;
                    }

                    .footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 0.8em;
                        color: #777;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }

                    /* Modal for lightbox */
                    .modal {
                        display: none;
                        position: fixed;
                        z-index: 1000;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        overflow: auto;
                        background-color: rgba(0,0,0,0.9);
                    }

                    .modal-content {
                        margin: auto;
                        display: block;
                        max-width: 90%;
                        max-height: 90%;
                    }

                    .modal-close {
                        position: absolute;
                        top: 15px;
                        right: 35px;
                        color: #f1f1f1;
                        font-size: 40px;
                        font-weight: bold;
                        transition: 0.3s;
                        cursor: pointer;
                    }

                    .modal-close:hover {
                        color: #bbb;
                    }

                    /* Icons */
                    .icon {
                        display: inline-block;
                        width: 24px;
                        height: 24px;
                        margin-right: 10px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                    }

                    .icon-patient {
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
                    }

                    .icon-diagnosis {
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
                    }

                    .icon-image {
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>');
                    }

                    .icon-recommendations {
                        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230277bd"><path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/></svg>');
                    }

                    /* Spine visualization styles */
                    .spine-visualization {
                        position: relative;
                        height: 350px;
                        border: 1px solid #ddd;
                        border-radius: var(--border-radius);
                        background-color: var(--light-bg);
                        margin-top: 15px;
                        overflow: hidden;
                        padding-top: 10px;
                    }

                    .spine {
                        position: relative;
                        width: 100%;
                        height: 100%;
                    }

                    .curve-info {
                        position: absolute;
                        top: 10px;
                        left: 50%;
                        transform: translateX(-50%);
                        text-align: center;
                        font-size: 14px;
                        padding: 5px 10px;
                        border-radius: 5px;
                        background-color: rgba(255,255,255,0.8);
                        z-index: 10;
                    }

                    .vertebra {
                        position: absolute;
                        width: 20px;
                        height: 10px;
                        background-color: var(--primary-color);
                        border-radius: 2px;
                        transition: all 0.3s ease;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                    }

                    .spine-label {
                        position: absolute;
                        left: 10px;
                        font-size: 10px;
                        color: var(--accent-color);
                        font-weight: bold;
                        text-shadow: 0 0 2px white;
                    }

                    .angle-marker {
                        position: absolute;
                        padding: 5px 10px;
                        background-color: rgba(255,255,255,0.9);
                        border-radius: 15px;
                        font-weight: bold;
                        color: var(--primary-color);
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        transition: all 0.3s ease;
                    }
                </style>
                <script>
                    // Add JavaScript for image modal functionality
                    function openModal() {
                        document.getElementById('imageModal').style.display = "block";
                        document.getElementById('modalImg').src = document.getElementById('xrayImage').src;
                    }

                    function closeModal() {
                        document.getElementById('imageModal').style.display = "none";
                    }

                    // Function to create spine visualization
                    function createSpineVisualization() {
                        const spineContainer = document.getElementById('spine-container');
                        if (!spineContainer) return;

                        // Clear any existing visualization
                        spineContainer.innerHTML = '';

                        // Get spine parameters from data attributes
                        const isDouble = true;
                        const isScoliosis = true;
                        const cobbAngle = 44.0;
                        const curveType = "Double Major (Right Thoracic, Left Lumbar)";
                        const severity = "Severe";

                        // Create spine container with data attributes for future updates
                        const spine = document.createElement('div');
                        spine.className = 'spine';
                        spine.dataset.cobbAngle = cobbAngle;
                        spine.dataset.isDouble = isDouble;
                        spine.dataset.isScoliosis = isScoliosis;
                        spine.dataset.curveType = curveType;
                        spine.dataset.severity = severity;
                        spineContainer.appendChild(spine);

                        // Create vertebrae
                        const vertebraeCount = 12;
                        const labels = ['T1', 'T3', 'T5', 'T7', 'T9', 'T11', 'L1', 'L3', 'L5'];

                        // Add a title with the curve information
                        const curveInfo = document.createElement('div');
                        curveInfo.className = 'curve-info';
                        if (isScoliosis) {
                            curveInfo.innerHTML = `<strong>${severity} Scoliosis</strong><br>Cobb Angle: ${cobbAngle}°<br>${curveType}`;
                            curveInfo.style.color = severity === 'Mild' ? '#2e7d32' : severity === 'Moderate' ? '#f57c00' : '#c62828';
                        } else {
                            curveInfo.innerHTML = '<strong>Normal Alignment</strong><br>No significant curvature';
                            curveInfo.style.color = '#2e7d32';
                        }
                        spine.appendChild(curveInfo);

                        for (let i = 0; i < vertebraeCount; i++) {
                            const vertebra = document.createElement('div');
                            vertebra.className = 'vertebra';

                            const height = 300;
                            const yPos = (i / (vertebraeCount - 1)) * (height - 20);

                            let xOffset = 0;
                            if (isScoliosis) {
                                if (isDouble) {
                                    // Create S-curve for double curve
                                    const phase = (i / (vertebraeCount - 1)) * Math.PI * 2;
                                    xOffset = Math.sin(phase) * (cobbAngle / 2);
                                } else {
                                    // Create C-curve for single curve
                                    const normalizedPos = (i / (vertebraeCount - 1)) * 2 - 1; // -1 to 1
                                    xOffset = Math.pow(normalizedPos, 2) * (cobbAngle / 2) * (normalizedPos < 0 ? -1 : 1);
                                }
                            }

                            vertebra.style.top = `${yPos}px`;
                            vertebra.style.left = `${50 + xOffset}px`;

                            // Color the vertebrae based on severity
                            if (isScoliosis) {
                                vertebra.style.backgroundColor = severity === 'Mild' ? '#81c784' :
                                                               severity === 'Moderate' ? '#ffb74d' : '#e57373';
                            }

                            spine.appendChild(vertebra);

                            // Add labels for some vertebrae
                            if (i % 2 === 0 || i === vertebraeCount - 1) {
                                const label = document.createElement('div');
                                label.className = 'spine-label';
                                label.textContent = labels[Math.floor(i / 2)];
                                label.style.top = `${yPos}px`;
                                spine.appendChild(label);
                            }
                        }

                        // Add Cobb angle marker
                        if (isScoliosis) {
                            const angleMarker = document.createElement('div');
                            angleMarker.className = 'angle-marker';
                            angleMarker.textContent = `${cobbAngle}°`;
                            angleMarker.style.top = '150px';
                            angleMarker.style.right = '10px';

                            // Color the angle marker based on severity
                            angleMarker.style.backgroundColor = severity === 'Mild' ? '#e8f5e9' :
                                                              severity === 'Moderate' ? '#fff8e1' : '#ffebee';
                            angleMarker.style.color = severity === 'Mild' ? '#2e7d32' :
                                                    severity === 'Moderate' ? '#f57c00' : '#c62828';
                            angleMarker.style.border = `2px solid ${severity === 'Mild' ? '#81c784' :
                                                                  severity === 'Moderate' ? '#ffb74d' : '#e57373'}`;

                            spine.appendChild(angleMarker);
                        }
                    }

                    // Initialize spine visualization when page loads
                    window.onload = function() {
                        createSpineVisualization();
                    };
                </script>
            </head>
            <body>
                <div class="report-container">
                    <div class="header">
                        <h1>Scoliosis Assessment Report</h1>
                        <p>Generated on 2025-06-29 14:00:32</p>
                    </div>

                    <div class="report-body">
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-patient"></span>Patient Information</h2>
                            <div class="patient-info">
                                <div class="patient-info-col">
                                    <div class="info-item">
                                        <strong>Patient ID:</strong> PT1751185832
                                    </div>
                                    <div class="info-item">
                                        <strong>Patient Name:</strong> Arbaz
                                    </div>
                                </div>
                                <div class="patient-info-col">
                                    <div class="info-item">
                                        <strong>Date:</strong> 2025-06-29 14:00:32
                                    </div>
                                    <div class="info-item">
                                        <strong>Report ID:</strong> SCL-85832
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-diagnosis"></span>Diagnosis</h2>
                            <div class="diagnosis-box">
                                <div class="diagnosis-summary">
                                    <div class="diagnosis-text">
                                        <p>No Scoliosis Detected 
Maintain healthy diet and exercise routine</p>
                                        <div class="severity-indicator severity-severe">
                                            Severe Scoliosis
                                        </div>
                                    </div>
                                    <div class="confidence-meter">
                                        <div>Confidence</div>
                                        <div class="confidence-value">97.2%</div>
                                    </div>
                                </div>

                                <div class="cobb-angle">
                                    <div class="cobb-angle-value">44.0°</div>
                                    <div>Cobb Angle Measurement</div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-diagnosis"></span>Curvature Analysis</h2>
                            <div class="diagnosis-box">
                                <div style="display: flex; flex-wrap: wrap;">
                                    <div style="flex: 1; min-width: 300px;">
                                        <div class="info-item">
                                            <strong>Curve Type:</strong> Double Major (Right Thoracic, Left Lumbar)
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Location:</strong> T5-T12
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Direction:</strong> Right
                                        </div>
                                        <div class="info-item">
                                            <strong>Curve Pattern:</strong> S-shaped
                                        </div>
                                    </div>
                                    <div style="flex: 1; min-width: 300px;">
                                        <div class="spine-visualization">
                                            <div id="spine-container"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-image"></span>X-Ray Analysis</h2>
            
                            <div class="image-section">
                                <div class="image-container">
                                    <img id="xrayImage" src="enhanced_1 (158).png" alt="Patient X-Ray" onclick="openModal()">
                                    <div class="image-caption">Posterior-Anterior (PA) view showing severe scoliotic curve with Cobb angle of 44.0°</div>
                                </div>
                            </div>

                            <!-- Modal for full-size image -->
                            <div id="imageModal" class="modal">
                                <span class="modal-close" onclick="closeModal()">&times;</span>
                                <img class="modal-content" id="modalImg">
                            </div>
                    
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Recommendations</h2>
                            <div class="recommendations">
                                <ul>
                                <li>Work with a physical therapist for customized exercises</li>
                <li>Schedule a follow-up appointment in 3 months</li>
                <li>Consider postural training to improve spinal alignment</li>
                <li>Maintain regular exercise routine focusing on core strength</li>

                                </ul>
                            </div>
                        </div>
                
                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Follow-up Plan</h2>
                            <div class="recommendations" style="border-left: 4px solid var(--primary-color);">
                                <p>Immediate consultation with an orthopedic specialist is recommended. Follow-up within 4-6 weeks.</p>
                            </div>
                        </div>

                        <div class="section">
                            <h2 class="section-title"><span class="icon icon-recommendations"></span>Disclaimer</h2>
                            <div class="recommendations" style="border-left: 4px solid #999;">
                                <p style="font-size: 0.9em; color: #666;">
                                    This report is generated by an automated system and should be reviewed by a qualified healthcare
                                    professional. The analysis is based on image processing algorithms and may not capture all clinical
                                    aspects of the patient's condition. This report is not a substitute for professional medical advice,
                                    diagnosis, or treatment.
                                </p>
                            </div>
                        </div>
            
                        <div class="download-section">
                            <h3>Download Options</h3>
                            <a href="PT1751185832_Arbaz_20250629140032.pdf" class="btn btn-primary" download>Download as PDF</a>
                            <button onclick="window.print()" class="btn btn-secondary">Print Report</button>
                        </div>
                    </div>

                    <div class="footer">
                        <p>This report was generated by the Scoliosis Detection System.</p>
                        <p>© 2025 Scoliosis Detection System | Confidential Medical Record</p>
                    </div>
                </div>
            </body>
            </html>
            