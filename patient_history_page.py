import os
import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from patient_history import PatientHistoryTracker

class PatientHistoryPage:
    def __init__(self, specific_patient_id=None, specific_patient_name=None):
        """Initialize the patient history page
        
        Args:
            specific_patient_id: If provided, will show only this patient
            specific_patient_name: Name of the specific patient
        """
        self.root = tk.Tk()
        self.root.title("Scoliosis Detection - Patient History Tracker")
        self.root.state('zoomed')  # Maximize window
        self.root.configure(bg="#f0f0f0")
        
        # Store specific patient info
        self.specific_patient_id = specific_patient_id
        self.specific_patient_name = specific_patient_name
        
        # Create header
        self.create_header()
        
        # Create main content
        self.create_main_content()
        
        # Initialize patient history tracker
        self.history_tracker = PatientHistoryTracker(self.main_frame)
        
        # Load patient data
        self.load_patient_data()
        
        # If specific patient is provided, select it
        if self.specific_patient_id:
            self.select_specific_patient()
    
    def create_header(self):
        """Create the header section of the page"""
        header_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        header_frame.pack(fill="x")
        
        # Title
        title_text = "Patient History Tracker"
        if self.specific_patient_name:
            title_text += f" - {self.specific_patient_name}"
            
        title_label = tk.Label(header_frame, text=title_text, 
                              font=("Arial", 24, "bold"), bg="#2c3e50", fg="white")
        title_label.pack(side="left", padx=20, pady=15)
        
        # Navigation buttons
        nav_frame = tk.Frame(header_frame, bg="#2c3e50")
        nav_frame.pack(side="right", padx=20)
        
        # Home button
        home_btn = tk.Button(nav_frame, text="Back to Main", command=self.go_to_main,
                           bg="#3498db", fg="white", font=("Arial", 12), padx=10)
        home_btn.pack(side="left", padx=5, pady=15)
        
        # Refresh button
        refresh_btn = tk.Button(nav_frame, text="Refresh Data", command=self.load_patient_data,
                              bg="#2ecc71", fg="white", font=("Arial", 12), padx=10)
        refresh_btn.pack(side="left", padx=5, pady=15)
        
        # View all patients button (only show if specific patient is selected)
        if self.specific_patient_id:
            all_patients_btn = tk.Button(nav_frame, text="View All Patients", 
                                      command=self.view_all_patients,
                                      bg="#e67e22", fg="white", font=("Arial", 12), padx=10)
            all_patients_btn.pack(side="left", padx=5, pady=15)
    
    def create_main_content(self):
        """Create the main content area"""
        self.main_frame = tk.Frame(self.root, bg="#f0f0f0")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create left panel for patient list (hide if specific patient)
        self.left_panel = tk.Frame(self.main_frame, bg="#f0f0f0", width=300)
        if not self.specific_patient_id:
            self.left_panel.pack(side="left", fill="y", padx=(0, 10))
        
        # Patient list header
        patient_header = tk.Frame(self.left_panel, bg="#f0f0f0")
        patient_header.pack(fill="x", pady=(0, 10))
        
        tk.Label(patient_header, text="Patients", font=("Arial", 16, "bold"), 
               bg="#f0f0f0").pack(side="left")
        
        # Search box
        self.search_var = tk.StringVar()
        self.search_var.trace("w", self.filter_patients)
        search_frame = tk.Frame(patient_header, bg="#f0f0f0")
        search_frame.pack(side="right")
        
        tk.Label(search_frame, text="Search:", bg="#f0f0f0").pack(side="left")
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=15)
        search_entry.pack(side="left", padx=5)
        
        # Create treeview for patient list
        patient_frame = tk.Frame(self.left_panel, bg="#f0f0f0")
        patient_frame.pack(fill="both", expand=True)
        
        columns = ("ID", "Name", "Visits", "Last Visit")
        self.patient_tree = ttk.Treeview(patient_frame, columns=columns, show="headings", height=20)
        
        # Define columns
        self.patient_tree.column("ID", width=80, anchor="center")
        self.patient_tree.column("Name", width=150, anchor="w")
        self.patient_tree.column("Visits", width=50, anchor="center")
        self.patient_tree.column("Last Visit", width=100, anchor="center")
        
        # Define headings
        self.patient_tree.heading("ID", text="Patient ID")
        self.patient_tree.heading("Name", text="Name")
        self.patient_tree.heading("Visits", text="Visits")
        self.patient_tree.heading("Last Visit", text="Last Visit")
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(patient_frame, orient="vertical", command=self.patient_tree.yview)
        self.patient_tree.configure(yscrollcommand=scrollbar.set)
        
        self.patient_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind selection event
        self.patient_tree.bind("<<TreeviewSelect>>", self.on_patient_select)
        
        # Add buttons
        button_frame = tk.Frame(self.left_panel, bg="#f0f0f0")
        button_frame.pack(fill="x", pady=10)
        
        add_btn = tk.Button(button_frame, text="Add Measurement", command=self.add_measurement,
                          bg="#27ae60", fg="white", font=("Arial", 11))
        add_btn.pack(side="left", padx=5)
        
        export_btn = tk.Button(button_frame, text="Export Graph", command=self.export_graph,
                             bg="#3498db", fg="white", font=("Arial", 11))
        export_btn.pack(side="left", padx=5)
        
        # Create right panel for patient details
        self.right_panel = tk.Frame(self.main_frame, bg="#f0f0f0")
        if self.specific_patient_id:
            # If specific patient, use full width
            self.right_panel.pack(fill="both", expand=True)
        else:
            # Otherwise, use right side
            self.right_panel.pack(side="right", fill="both", expand=True)
        
        # Patient details section
        self.details_frame = tk.LabelFrame(self.right_panel, text="Patient Details", 
                                         font=("Arial", 12, "bold"), bg="#f0f0f0")
        self.details_frame.pack(fill="x", pady=(0, 10))
        
        # Patient info
        info_frame = tk.Frame(self.details_frame, bg="#f0f0f0")
        info_frame.pack(fill="x", padx=10, pady=10)
        
        # Create grid for patient info
        tk.Label(info_frame, text="Patient ID:", bg="#f0f0f0", font=("Arial", 11)).grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.patient_id_var = tk.StringVar()
        tk.Label(info_frame, textvariable=self.patient_id_var, bg="#f0f0f0", font=("Arial", 11, "bold")).grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        tk.Label(info_frame, text="Patient Name:", bg="#f0f0f0", font=("Arial", 11)).grid(row=0, column=2, sticky="w", padx=5, pady=2)
        self.patient_name_var = tk.StringVar()
        tk.Label(info_frame, textvariable=self.patient_name_var, bg="#f0f0f0", font=("Arial", 11, "bold")).grid(row=0, column=3, sticky="w", padx=5, pady=2)
        
        tk.Label(info_frame, text="Total Visits:", bg="#f0f0f0", font=("Arial", 11)).grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.visits_var = tk.StringVar()
        tk.Label(info_frame, textvariable=self.visits_var, bg="#f0f0f0", font=("Arial", 11)).grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        tk.Label(info_frame, text="Last Visit:", bg="#f0f0f0", font=("Arial", 11)).grid(row=1, column=2, sticky="w", padx=5, pady=2)
        self.last_visit_var = tk.StringVar()
        tk.Label(info_frame, textvariable=self.last_visit_var, bg="#f0f0f0", font=("Arial", 11)).grid(row=1, column=3, sticky="w", padx=5, pady=2)
        
        # Add measurement button for specific patient view
        if self.specific_patient_id:
            add_btn_frame = tk.Frame(self.details_frame, bg="#f0f0f0")
            add_btn_frame.pack(fill="x", padx=10, pady=(0, 10))
            
            add_btn = tk.Button(add_btn_frame, text="Add New Measurement", command=self.add_measurement,
                              bg="#27ae60", fg="white", font=("Arial", 11), padx=10)
            add_btn.pack(side="left", padx=5)
            
            export_btn = tk.Button(add_btn_frame, text="Export Progress Graph", command=self.export_graph,
                                 bg="#3498db", fg="white", font=("Arial", 11), padx=10)
            export_btn.pack(side="left", padx=5)
        
        # Measurements history
        self.history_frame = tk.LabelFrame(self.right_panel, text="Measurement History", 
                                         font=("Arial", 12, "bold"), bg="#f0f0f0")
        self.history_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Create treeview for measurements
        columns = ("Date", "Cobb Angle", "Severity", "Confidence", "Notes")
        self.history_tree = ttk.Treeview(self.history_frame, columns=columns, show="headings", height=8)
        
        # Define columns
        self.history_tree.column("Date", width=150, anchor="center")
        self.history_tree.column("Cobb Angle", width=100, anchor="center")
        self.history_tree.column("Severity", width=100, anchor="center")
        self.history_tree.column("Confidence", width=100, anchor="center")
        self.history_tree.column("Notes", width=300, anchor="w")
        
        # Define headings
        self.history_tree.heading("Date", text="Date")
        self.history_tree.heading("Cobb Angle", text="Cobb Angle (°)")
        self.history_tree.heading("Severity", text="Severity")
        self.history_tree.heading("Confidence", text="Confidence (%)")
        self.history_tree.heading("Notes", text="Notes")
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        self.history_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)
        
        # Graph frame
        self.graph_frame = tk.LabelFrame(self.right_panel, text="Progress Graph", 
                                       font=("Arial", 12, "bold"), bg="#f0f0f0")
        self.graph_frame.pack(fill="both", expand=True)
        
        # Create matplotlib figure
        self.fig = plt.Figure(figsize=(8, 4), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, self.graph_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize with empty graph
        self.ax.text(0.5, 0.5, 'Select a patient to view progress graph', 
                   horizontalalignment='center', verticalalignment='center',
                   transform=self.ax.transAxes, fontsize=12)
        self.canvas.draw()
    
    def load_patient_data(self):
        """Load patient data into the treeview"""
        # Skip if we're only showing a specific patient
        if self.specific_patient_id and not self.left_panel.winfo_ismapped():
            return
            
        # Clear existing items
        for item in self.patient_tree.get_children():
            self.patient_tree.delete(item)
        
        # Get all patients
        patients = self.history_tracker.get_all_patients()
        
        # Insert into treeview
        for patient in patients:
            patient_id, name, visit_count, last_visit = patient
            # Format the last visit date
            if last_visit:
                last_visit_date = datetime.strptime(last_visit, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
            else:
                last_visit_date = "N/A"
                visit_count = 0
            
            self.patient_tree.insert("", "end", values=(patient_id, name, visit_count, last_visit_date))
    
    def select_specific_patient(self):
        """Select and display the specific patient"""
        if not self.specific_patient_id:
            return
            
        # Get patient history
        history = self.history_tracker.get_patient_history(self.specific_patient_id)
        if not history:
            messagebox.showinfo("Patient Not Found", 
                              f"No history found for patient ID: {self.specific_patient_id}")
            return
        
        # Update patient info
        self.patient_id_var.set(history['patient_id'])
        self.patient_name_var.set(history['patient_name'])
        self.visits_var.set(str(len(history['measurements'])))
        if history['measurements']:
            self.last_visit_var.set(datetime.strptime(history['measurements'][-1][0], "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d"))
        else:
            self.last_visit_var.set("N/A")
        
        # Clear history tree
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # Add measurements to history tree
        for measurement in history['measurements']:
            timestamp, cobb_angle, severity, confidence, notes = measurement
            date = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
            cobb_angle_str = f"{cobb_angle:.1f}°" if cobb_angle is not None else "N/A"
            confidence_str = f"{confidence:.1f}%" if confidence is not None else "N/A"
            
            self.history_tree.insert("", "end", values=(date, cobb_angle_str, severity, confidence_str, notes))
        
        # Update graph
        self.ax.clear()
        
        if history['measurements']:
            dates = [datetime.strptime(m[0], "%Y-%m-%d %H:%M:%S") for m in history['measurements']]
            cobb_angles = [m[1] if m[1] is not None else 0 for m in history['measurements']]
            
            # Plot data
            self.ax.plot(dates, cobb_angles, 'o-', color='#0277bd', linewidth=2, markersize=8)
            
            # Add reference lines for severity levels
            self.ax.axhline(y=10, color='#81c784', linestyle='--', alpha=0.5, label='Mild')
            self.ax.axhline(y=25, color='#ffb74d', linestyle='--', alpha=0.5, label='Moderate')
            self.ax.axhline(y=40, color='#e57373', linestyle='--', alpha=0.5, label='Severe')
            
            # Format the graph
            self.ax.set_xlabel('Date')
            self.ax.set_ylabel('Cobb Angle (°)')
            self.ax.set_title(f'Scoliosis Progression - {history["patient_name"]}')
            self.ax.grid(True, linestyle='--', alpha=0.7)
            self.ax.legend()
            
            # Format x-axis dates
            self.fig.autofmt_xdate()
            
            # Set y-axis limits
            max_angle = max(cobb_angles) if cobb_angles else 50
            self.ax.set_ylim(0, max(50, max_angle * 1.2))
        else:
            self.ax.text(0.5, 0.5, 'No measurement data available', 
                       horizontalalignment='center', verticalalignment='center',
                       transform=self.ax.transAxes)
        
        self.canvas.draw()
    
    def view_all_patients(self):
        """Switch to view all patients mode"""
        self.specific_patient_id = None
        self.specific_patient_name = None
        
        # Destroy current window and create new one
        self.root.destroy()
        app = PatientHistoryPage()
        app.run()
    
    def filter_patients(self, *args):
        """Filter patients based on search text"""
        search_text = self.search_var.get().lower()
        
        # Clear existing items
        for item in self.patient_tree.get_children():
            self.patient_tree.delete(item)
        
        # Get all patients
        patients = self.history_tracker.get_all_patients()
        
        # Filter and insert into treeview
        for patient in patients:
            patient_id, name, visit_count, last_visit = patient
            
            # Check if search text is in patient ID or name
            if search_text in patient_id.lower() or search_text in name.lower():
                # Format the last visit date
                if last_visit:
                    last_visit_date = datetime.strptime(last_visit, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
                else:
                    last_visit_date = "N/A"
                    visit_count = 0
                
                self.patient_tree.insert("", "end", values=(patient_id, name, visit_count, last_visit_date))
    
    def on_patient_select(self, event):
        """Handle patient selection"""
        selected_item = self.patient_tree.selection()
        if not selected_item:
            return
        
        # Get patient ID
        patient_id = self.patient_tree.item(selected_item[0], "values")[0]
        
        # Get patient history
        history = self.history_tracker.get_patient_history(patient_id)
        if not history:
            return
        
        # Update patient info
        self.patient_id_var.set(history['patient_id'])
        self.patient_name_var.set(history['patient_name'])
        self.visits_var.set(str(len(history['measurements'])))
        if history['measurements']:
            self.last_visit_var.set(datetime.strptime(history['measurements'][-1][0], "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d"))
        else:
            self.last_visit_var.set("N/A")
        
        # Clear history tree
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # Add measurements to history tree
        for measurement in history['measurements']:
            timestamp, cobb_angle, severity, confidence, notes = measurement
            date = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
            cobb_angle_str = f"{cobb_angle:.1f}°" if cobb_angle is not None else "N/A"
            confidence_str = f"{confidence:.1f}%" if confidence is not None else "N/A"
            
            self.history_tree.insert("", "end", values=(date, cobb_angle_str, severity, confidence_str, notes))
        
        # Update graph
        self.ax.clear()
        
        if history['measurements']:
            dates = [datetime.strptime(m[0], "%Y-%m-%d %H:%M:%S") for m in history['measurements']]
            cobb_angles = [m[1] if m[1] is not None else 0 for m in history['measurements']]
            
            # Plot data
            self.ax.plot(dates, cobb_angles, 'o-', color='#0277bd', linewidth=2, markersize=8)
            
            # Add reference lines for severity levels
            self.ax.axhline(y=10, color='#81c784', linestyle='--', alpha=0.5, label='Mild')
            self.ax.axhline(y=25, color='#ffb74d', linestyle='--', alpha=0.5, label='Moderate')
            self.ax.axhline(y=40, color='#e57373', linestyle='--', alpha=0.5, label='Severe')
            
            # Format the graph
            self.ax.set_xlabel('Date')
            self.ax.set_ylabel('Cobb Angle (°)')
            self.ax.set_title(f'Scoliosis Progression - {history["patient_name"]}')
            self.ax.grid(True, linestyle='--', alpha=0.7)
            self.ax.legend()
            
            # Format x-axis dates
            self.fig.autofmt_xdate()
            
            # Set y-axis limits
            max_angle = max(cobb_angles) if cobb_angles else 50
            self.ax.set_ylim(0, max(50, max_angle * 1.2))
        else:
            self.ax.text(0.5, 0.5, 'No measurement data available', 
                       horizontalalignment='center', verticalalignment='center',
                       transform=self.ax.transAxes)
        
        self.canvas.draw()
    
    def add_measurement(self):
        """Add a new measurement for the selected patient"""
        selected_item = self.patient_tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Patient", "Please select a patient first")
            return
        
        # Get patient ID and name
        patient_id = self.patient_tree.item(selected_item[0], "values")[0]
        patient_name = self.patient_tree.item(selected_item[0], "values")[1]
        
        # Create add measurement dialog
        add_window = tk.Toplevel(self.root)
        add_window.title(f"Add Measurement for {patient_name}")
        add_window.geometry("400x350")
        add_window.resizable(False, False)
        add_window.transient(self.root)
        add_window.grab_set()
        
        # Style the window
        add_window.configure(bg="#f0f0f0")
        
        # Add header
        header = tk.Label(add_window, text=f"New Measurement for {patient_name}", 
                        font=("Arial", 14, "bold"), bg="#f0f0f0", pady=10)
        header.pack(fill="x")
        
        # Add form frame
        form_frame = tk.Frame(add_window, bg="#f0f0f0", padx=20, pady=10)
        form_frame.pack(fill="both", expand=True)
        
        # Add form fields
        tk.Label(form_frame, text="Cobb Angle (°):", bg="#f0f0f0", font=("Arial", 11)).grid(row=0, column=0, padx=10, pady=10, sticky="w")
        cobb_angle_var = tk.DoubleVar(value=0.0)
        ttk.Spinbox(form_frame, from_=0, to=90, increment=0.5, textvariable=cobb_angle_var, width=10).grid(row=0, column=1, padx=10, pady=10, sticky="w")
        
        tk.Label(form_frame, text="Severity:", bg="#f0f0f0", font=("Arial", 11)).grid(row=1, column=0, padx=10, pady=10, sticky="w")
        severity_var = tk.StringVar(value="Mild")
        ttk.Combobox(form_frame, textvariable=severity_var, values=["Mild", "Moderate", "Severe"], state="readonly", width=10).grid(row=1, column=1, padx=10, pady=10, sticky="w")
        
        tk.Label(form_frame, text="Confidence (%):", bg="#f0f0f0", font=("Arial", 11)).grid(row=2, column=0, padx=10, pady=10, sticky="w")
        confidence_var = tk.DoubleVar(value=85.0)
        ttk.Spinbox(form_frame, from_=0, to=100, increment=0.1, textvariable=confidence_var, width=10).grid(row=2, column=1, padx=10, pady=10, sticky="w")
        
        tk.Label(form_frame, text="Notes:", bg="#f0f0f0", font=("Arial", 11)).grid(row=3, column=0, padx=10, pady=10, sticky="nw")
        notes_text = tk.Text(form_frame, width=30, height=5)
        notes_text.grid(row=3, column=1, padx=10, pady=10, sticky="w")
        
        # Add buttons
        button_frame = tk.Frame(add_window, bg="#f0f0f0")
        button_frame.pack(fill="x", pady=10)
        
        def save_measurement():
            try:
                cobb_angle = cobb_angle_var.get()
                severity = severity_var.get()
                confidence = confidence_var.get()
                notes = notes_text.get("1.0", "end-1c")
                
                # Add measurement to database
                success = self.history_tracker.add_measurement(patient_id, cobb_angle, severity, confidence, notes)
                
                if success:
                    messagebox.showinfo("Success", "Measurement added successfully")
                    add_window.destroy()
                    
                    # Refresh patient list
                    self.load_patient_data()
                    
                    # Reselect the patient
                    for item in self.patient_tree.get_children():
                        if self.patient_tree.item(item, "values")[0] == patient_id:
                            self.patient_tree.selection_set(item)
                            self.patient_tree.see(item)
                            self.on_patient_select(None)
                            break
                else:
                    messagebox.showerror("Error", "Failed to add measurement")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")
        
        save_btn = tk.Button(button_frame, text="Save", command=save_measurement,
                           bg="#27ae60", fg="white", font=("Arial", 11), width=10)
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = tk.Button(button_frame, text="Cancel", command=add_window.destroy,
                             bg="#e74c3c", fg="white", font=("Arial", 11), width=10)
        cancel_btn.pack(side="right", padx=10)
    
    def export_graph(self):
        """Export the current graph as an image"""
        selected_item = self.patient_tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Patient", "Please select a patient first")
            return
        
        # Get patient ID and name
        patient_id = self.patient_tree.item(selected_item[0], "values")[0]
        patient_name = self.patient_tree.item(selected_item[0], "values")[1]
        
        # Create reports directory if it doesn't exist
        os.makedirs("reports", exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reports/progress_graph_{patient_id}_{timestamp}.png"
        
        # Save figure
        try:
            self.fig.savefig(filename, dpi=300, bbox_inches='tight')
            messagebox.showinfo("Success", f"Graph exported successfully to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export graph: {str(e)}")
    
    def go_to_main(self):
        """Return to the main application"""
        self.root.destroy()
        from subprocess import call
        call(["python", "GUI_Master_New.py"])
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

# Add this to make the file executable
if __name__ == "__main__":
    import sys
    
    # Check if patient ID and name are provided as command-line arguments
    if len(sys.argv) >= 3:
        patient_id = sys.argv[1]
        patient_name = sys.argv[2]
        app = PatientHistoryPage(specific_patient_id=patient_id, specific_patient_name=patient_name)
    else:
        app = PatientHistoryPage()
    
    app.run()

