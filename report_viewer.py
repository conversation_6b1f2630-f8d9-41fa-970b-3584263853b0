import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from PIL import Image, ImageTk
import datetime
import shutil
from patient_database import get_db_instance

# Define color scheme
PRIMARY_COLOR = "#2c3e50"  # Dark blue-gray
SECONDARY_COLOR = "#3498db"  # Bright blue
ACCENT_COLOR = "#e74c3c"  # Red
BG_COLOR = "#ecf0f1"  # Light gray
TEXT_COLOR = "#2c3e50"  # Dark blue-gray
HIGHLIGHT_COLOR = "#2ecc71"  # Green

class ReportViewer:
    def __init__(self, root):
        """Initialize the report viewer"""
        self.root = root
        self.db = get_db_instance()
        self.setup_ui()
        self.load_reports()

    def setup_ui(self):
        """Set up the main UI for viewing reports"""
        self.root.title("Report Viewer")
        self.root.configure(bg=BG_COLOR)

        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Set window size (80% of screen)
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        self.root.geometry(f"{window_width}x{window_height}")

        # Create styles
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('Header.TLabel', font=('Segoe UI', 16, 'bold'), background=PRIMARY_COLOR, foreground='white')
        self.style.configure('Subheader.TLabel', font=('Segoe UI', 12, 'bold'), background=BG_COLOR, foreground=TEXT_COLOR)

        # Create header
        self.header_frame = ttk.Frame(self.root, style='TFrame')
        self.header_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(self.header_frame, text="Patient Reports", style='Header.TLabel').pack(side='left')

        # Create search frame
        self.search_frame = ttk.Frame(self.root, style='TFrame')
        self.search_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(self.search_frame, text="Search by patient name:").pack(side='left', padx=5)

        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left', padx=5)

        search_btn = ttk.Button(self.search_frame, text="Search", command=self.search_reports)
        search_btn.pack(side='left', padx=5)

        # Create filter frame
        self.filter_frame = ttk.Frame(self.root, style='TFrame')
        self.filter_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(self.filter_frame, text="Filter by diagnosis:").pack(side='left', padx=5)

        self.filter_var = tk.StringVar()
        self.filter_var.set("All")
        filter_options = ["All", "Scoliosis Detected", "No Scoliosis Detected"]
        filter_dropdown = ttk.Combobox(self.filter_frame, textvariable=self.filter_var, values=filter_options, state="readonly", width=20)
        filter_dropdown.pack(side='left', padx=5)

        filter_btn = ttk.Button(self.filter_frame, text="Apply Filter", command=self.filter_reports)
        filter_btn.pack(side='left', padx=5)

        # Create main content area - split into two panes
        self.content_frame = ttk.Frame(self.root, style='TFrame')
        self.content_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create a PanedWindow
        self.paned_window = ttk.PanedWindow(self.content_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill='both', expand=True)

        # Left pane - Report list
        self.report_frame = ttk.Frame(self.paned_window, style='TFrame')
        self.paned_window.add(self.report_frame, weight=1)

        # Create a treeview for reports
        self.report_tree = ttk.Treeview(self.report_frame, columns=('date', 'patient', 'diagnosis'), show='headings')
        self.report_tree.heading('date', text='Date')
        self.report_tree.heading('patient', text='Patient')
        self.report_tree.heading('diagnosis', text='Diagnosis')
        self.report_tree.column('date', width=150)
        self.report_tree.column('patient', width=150)
        self.report_tree.column('diagnosis', width=200)
        self.report_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Add scrollbar to treeview
        scrollbar = ttk.Scrollbar(self.report_tree, orient="vertical", command=self.report_tree.yview)
        scrollbar.pack(side='right', fill='y')
        self.report_tree.configure(yscrollcommand=scrollbar.set)

        # Bind selection event
        self.report_tree.bind('<<TreeviewSelect>>', self.on_report_selected)

        # Right pane - Report details
        self.detail_frame = ttk.Frame(self.paned_window, style='TFrame')
        self.paned_window.add(self.detail_frame, weight=2)

        # Create a frame for report details
        self.detail_content = ttk.Frame(self.detail_frame, style='TFrame')
        self.detail_content.pack(fill='both', expand=True, padx=5, pady=5)

        # Placeholder for when no report is selected
        self.placeholder_label = ttk.Label(self.detail_content, text="Select a report to view details")
        self.placeholder_label.pack(pady=50)

        # Create a status bar
        self.status_bar = ttk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def load_reports(self):
        """Load all reports from the database"""
        # Clear existing items
        for item in self.report_tree.get_children():
            self.report_tree.delete(item)

        # Get recent examinations from the database
        examinations = self.db.get_recent_examinations(limit=100)

        # Add examinations to the treeview
        for exam in examinations:
            if exam['report_path'] or exam['html_report_path']:
                self.report_tree.insert('', 'end', values=(
                    self.format_date(exam['exam_date']),
                    exam['patient_name'],
                    exam['diagnosis']
                ), tags=(str(exam['id']),))

        # Update status bar
        self.status_bar.config(text=f"Loaded {len(examinations)} reports")

    def search_reports(self):
        """Search for reports by patient name"""
        search_term = self.search_var.get().strip()

        if not search_term:
            self.load_reports()
            return

        # Clear existing items
        for item in self.report_tree.get_children():
            self.report_tree.delete(item)

        # Get patients matching the search term
        patients = self.db.get_patient_by_name(search_term)

        if not patients:
            self.status_bar.config(text="No patients found matching the search term")
            return

        # Get examinations for each patient
        all_exams = []
        for patient in patients:
            exams = self.db.get_patient_examinations(patient['id'])
            all_exams.extend(exams)

        # Sort by date (newest first)
        all_exams.sort(key=lambda x: x['exam_date'], reverse=True)

        # Add examinations to the treeview
        count = 0
        for exam in all_exams:
            if exam['report_path'] or exam['html_report_path']:
                patient = self.db.get_patient(exam['patient_id'])
                self.report_tree.insert('', 'end', values=(
                    self.format_date(exam['exam_date']),
                    patient['name'],
                    exam['diagnosis']
                ), tags=(str(exam['id']),))
                count += 1

        # Update status bar
        self.status_bar.config(text=f"Found {count} reports for search term: {search_term}")

    def filter_reports(self):
        """Filter reports by diagnosis"""
        filter_value = self.filter_var.get()

        if filter_value == "All":
            self.load_reports()
            return

        # Clear existing items
        for item in self.report_tree.get_children():
            self.report_tree.delete(item)

        # Get recent examinations from the database
        examinations = self.db.get_recent_examinations(limit=100)

        # Filter by diagnosis
        filtered_exams = [exam for exam in examinations if filter_value in exam['diagnosis']]

        # Add examinations to the treeview
        for exam in filtered_exams:
            if exam['report_path'] or exam['html_report_path']:
                self.report_tree.insert('', 'end', values=(
                    self.format_date(exam['exam_date']),
                    exam['patient_name'],
                    exam['diagnosis']
                ), tags=(str(exam['id']),))

        # Update status bar
        self.status_bar.config(text=f"Found {len(filtered_exams)} reports with diagnosis: {filter_value}")

    def on_report_selected(self, event):
        """Handle selection of a report"""
        selected_items = self.report_tree.selection()
        if not selected_items:
            return

        # Get the selected examination's ID
        item = selected_items[0]
        exam_id = int(self.report_tree.item(item, 'tags')[0])

        # Load report details
        self.load_report_details(exam_id)

    def load_report_details(self, exam_id):
        """Load and display report details"""
        # Get examination from the database
        exam = self.db.get_examination(exam_id)
        if not exam:
            messagebox.showerror("Error", f"Examination with ID {exam_id} not found.")
            return

        # Get patient information
        patient = self.db.get_patient(exam['patient_id'])
        if not patient:
            messagebox.showerror("Error", f"Patient with ID {exam['patient_id']} not found.")
            return

        # Clear the detail content frame
        for widget in self.detail_content.winfo_children():
            widget.destroy()

        # Create a canvas with scrollbar for the details
        canvas = tk.Canvas(self.detail_content, bg=BG_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.detail_content, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add report details to the scrollable frame
        # Patient information
        patient_frame = ttk.Frame(scrollable_frame, style='TFrame')
        patient_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(patient_frame, text="Patient:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(patient_frame, text=patient['name']).pack(side='left', padx=5)

        ttk.Label(patient_frame, text="ID:", font=('Segoe UI', 10, 'bold')).pack(side='left', padx=(20, 0))
        ttk.Label(patient_frame, text=patient['id']).pack(side='left', padx=5)

        # Date and time
        date_frame = ttk.Frame(scrollable_frame, style='TFrame')
        date_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(date_frame, text="Date:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(date_frame, text=self.format_date(exam['exam_date'])).pack(side='left', padx=5)

        # Diagnosis
        diag_frame = ttk.Frame(scrollable_frame, style='TFrame')
        diag_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(diag_frame, text="Diagnosis:", font=('Segoe UI', 10, 'bold')).pack(side='left')
        ttk.Label(diag_frame, text=exam['diagnosis']).pack(side='left', padx=5)

        # Confidence score
        if exam['confidence']:
            conf_frame = ttk.Frame(scrollable_frame, style='TFrame')
            conf_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(conf_frame, text="Confidence Score:", font=('Segoe UI', 10, 'bold')).pack(side='left')
            ttk.Label(conf_frame, text=f"{exam['confidence']:.2f}%").pack(side='left', padx=5)

        # Add separator
        ttk.Separator(scrollable_frame, orient='horizontal').pack(fill='x', padx=10, pady=10)

        # X-ray image
        if exam['image_path'] and os.path.exists(exam['image_path']):
            img_frame = ttk.Frame(scrollable_frame, style='TFrame')
            img_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(img_frame, text="X-ray Image:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

            try:
                # Load and resize the image
                img = Image.open(exam['image_path'])
                img.thumbnail((300, 300))  # Resize to fit
                photo = ImageTk.PhotoImage(img)

                # Display the image
                img_label = ttk.Label(img_frame, image=photo)
                img_label.image = photo  # Keep a reference
                img_label.pack(pady=5)
            except Exception as e:
                ttk.Label(img_frame, text=f"Error loading image: {str(e)}").pack(pady=5)

        # Report actions
        action_frame = ttk.Frame(scrollable_frame, style='TFrame')
        action_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(action_frame, text="Report Actions:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

        button_frame = ttk.Frame(action_frame, style='TFrame')
        button_frame.pack(fill='x', pady=5)

        # View PDF report
        if exam['report_path'] and os.path.exists(exam['report_path']):
            pdf_btn = ttk.Button(button_frame, text="View PDF Report",
                               command=lambda: self.open_file(exam['report_path']))
            pdf_btn.pack(side='left', padx=5, pady=5)

            # Export PDF report
            export_pdf_btn = ttk.Button(button_frame, text="Export PDF",
                                      command=lambda: self.export_file(exam['report_path']))
            export_pdf_btn.pack(side='left', padx=5, pady=5)

        # View HTML report
        if exam['html_report_path'] and os.path.exists(exam['html_report_path']):
            html_btn = ttk.Button(button_frame, text="View HTML Report",
                                command=lambda: self.open_file(exam['html_report_path']))
            html_btn.pack(side='left', padx=5, pady=5)

            # Export HTML report
            export_html_btn = ttk.Button(button_frame, text="Export HTML",
                                       command=lambda: self.export_file(exam['html_report_path']))
            export_html_btn.pack(side='left', padx=5, pady=5)

        # Email report button
        if exam['report_path'] and os.path.exists(exam['report_path']):
            email_btn = ttk.Button(button_frame, text="Email Report",
                               command=lambda: self.email_report(exam['report_path'], patient['name']))
            email_btn.pack(side='left', padx=5, pady=5)

        # Patient history button
        history_btn = ttk.Button(scrollable_frame, text="View Patient History",
                               command=lambda: self.view_patient_history(patient['id'], patient['name']))
        history_btn.pack(anchor='w', padx=10, pady=10)

        # Notes section
        if exam['notes']:
            notes_frame = ttk.Frame(scrollable_frame, style='TFrame')
            notes_frame.pack(fill='x', padx=10, pady=10)

            ttk.Label(notes_frame, text="Notes:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')

            # Create a text widget for notes (read-only)
            notes_text = tk.Text(notes_frame, height=5, width=40, wrap='word', state='disabled')
            notes_text.pack(fill='x', pady=5)

            # Insert the notes
            notes_text.configure(state='normal')
            notes_text.insert('1.0', exam['notes'])
            notes_text.configure(state='disabled')

    def export_file(self, file_path):
        """Export a file to a user-selected location"""
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "File not found.")
            return

        # Get the file name
        file_name = os.path.basename(file_path)

        # Ask the user for a save location
        save_path = filedialog.asksaveasfilename(
            initialfile=file_name,
            defaultextension=os.path.splitext(file_name)[1],
            filetypes=[("All Files", "*.*")]
        )

        if not save_path:
            return  # User canceled

        try:
            # Copy the file
            shutil.copy2(file_path, save_path)
            messagebox.showinfo("Success", f"File exported successfully to {save_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Error exporting file: {str(e)}")

    def email_report(self, report_path, patient_name):
        """Email a report to a recipient"""
        try:
            # Import required modules
            from report_system import ReportGenerator
            from email_dialog import EmailDialog

            # Create ReportGenerator instance with existing report
            report_gen = ReportGenerator(patient_name)
            report_gen.filename = report_path

            # Show email dialog
            email_dialog = EmailDialog(self.root, report_gen)
            result = email_dialog.show()

            # Show result message if needed
            if result["success"]:
                messagebox.showinfo("Success", "Email sent successfully!")
        except Exception as e:
            messagebox.showerror("Email Error", f"Failed to send email: {str(e)}")

    def view_patient_history(self, patient_id, patient_name):
        """Open the patient history viewer for a specific patient"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "patient_history_viewer.py", patient_id, patient_name])
        except Exception as e:
            messagebox.showerror("Error", f"Error opening patient history: {str(e)}")

    def open_file(self, file_path):
        """Open a file with the default application"""
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "File not found.")
            return

        try:
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            else:  # Linux
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            messagebox.showerror("Error", f"Error opening file: {str(e)}")

    def format_date(self, date_str):
        """Format a date string for display"""
        try:
            dt = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            return dt.strftime("%b %d, %Y %I:%M %p")
        except:
            return date_str


def main():
    """Main function to run the report viewer"""
    root = tk.Tk()
    app = ReportViewer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
