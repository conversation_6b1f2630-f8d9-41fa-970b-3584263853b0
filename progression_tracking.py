"""
Progression Tracking Module
This module compares measurements across multiple scans to track changes in scoliosis over time.
"""

import os
import json
import datetime
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import sqlite3
import cv2

class ProgressionTracker:
    """Track scoliosis progression over time"""
    
    def __init__(self, database_path="patient_data.db"):
        """Initialize the progression tracker
        
        Args:
            database_path: Path to the patient database
        """
        self.database_path = database_path
        self.initialize_database()
    
    def initialize_database(self):
        """Initialize the database if it doesn't exist"""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            date_of_birth TEXT,
            gender TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS examinations (
            id INTEGER PRIMARY KEY,
            patient_id INTEGER,
            date TEXT NOT NULL,
            image_path TEXT,
            analysis_path TEXT,
            notes TEXT,
            FOREIGN KEY (patient_id) REFERENCES patients (id)
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cobb_angles (
            id INTEGER PRIMARY KEY,
            examination_id INTEGER,
            upper_vertebra TEXT NOT NULL,
            lower_vertebra TEXT NOT NULL,
            angle REAL NOT NULL,
            is_primary BOOLEAN,
            FOREIGN KEY (examination_id) REFERENCES examinations (id)
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_patient(self, name, date_of_birth=None, gender=None):
        """Add a new patient to the database
        
        Args:
            name: Patient name
            date_of_birth: Patient date of birth (YYYY-MM-DD)
            gender: Patient gender
            
        Returns:
            Patient ID
        """
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO patients (name, date_of_birth, gender)
        VALUES (?, ?, ?)
        ''', (name, date_of_birth, gender))
        
        patient_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return patient_id
    
    def add_examination(self, patient_id, date, image_path, analysis_results, notes=None):
        """Add a new examination for a patient
        
        Args:
            patient_id: Patient ID
            date: Examination date (YYYY-MM-DD)
            image_path: Path to the X-ray image
            analysis_results: Dictionary with analysis results
            notes: Additional notes
            
        Returns:
            Examination ID
        """
        # Save analysis results to a JSON file
        os.makedirs("analysis", exist_ok=True)
        analysis_filename = f"analysis/patient_{patient_id}_{date.replace('-', '')}.json"
        
        with open(analysis_filename, 'w') as f:
            json.dump(analysis_results, f, indent=4)
        
        # Add examination to database
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO examinations (patient_id, date, image_path, analysis_path, notes)
        VALUES (?, ?, ?, ?, ?)
        ''', (patient_id, date, image_path, analysis_filename, notes))
        
        examination_id = cursor.lastrowid
        
        # Add Cobb angles to database
        for i, cobb_angle in enumerate(analysis_results["cobb_angles"]):
            is_primary = (i == 0)  # Assume first angle is primary
            if analysis_results["primary_curve"]:
                is_primary = (cobb_angle["angle"] == analysis_results["primary_curve"]["angle"])
            
            cursor.execute('''
            INSERT INTO cobb_angles (examination_id, upper_vertebra, lower_vertebra, angle, is_primary)
            VALUES (?, ?, ?, ?, ?)
            ''', (
                examination_id,
                cobb_angle["upper_vertebra"],
                cobb_angle["lower_vertebra"],
                cobb_angle["angle"],
                is_primary
            ))
        
        conn.commit()
        conn.close()
        
        return examination_id
    
    def get_patient_examinations(self, patient_id):
        """Get all examinations for a patient
        
        Args:
            patient_id: Patient ID
            
        Returns:
            List of examination dictionaries
        """
        conn = sqlite3.connect(self.database_path)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT e.*, p.name as patient_name
        FROM examinations e
        JOIN patients p ON e.patient_id = p.id
        WHERE e.patient_id = ?
        ORDER BY e.date DESC
        ''', (patient_id,))
        
        examinations = [dict(row) for row in cursor.fetchall()]
        
        # Get Cobb angles for each examination
        for exam in examinations:
            cursor.execute('''
            SELECT * FROM cobb_angles
            WHERE examination_id = ?
            ORDER BY is_primary DESC
            ''', (exam["id"],))
            
            exam["cobb_angles"] = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        return examinations
    
    def get_progression_data(self, patient_id, curve_type=None):
        """Get progression data for a patient
        
        Args:
            patient_id: Patient ID
            curve_type: Type of curve to track (e.g., "T5-T12")
                        If None, track the primary curve
            
        Returns:
            Dictionary with progression data
        """
        examinations = self.get_patient_examinations(patient_id)
        
        if not examinations:
            return {"error": "No examinations found for this patient"}
        
        # Extract dates and angles
        dates = []
        angles = []
        curve_info = []
        
        for exam in examinations:
            # Find the relevant Cobb angle
            cobb_angle = None
            
            if curve_type:
                # Find the specified curve type
                for angle in exam["cobb_angles"]:
                    curve = f"{angle['upper_vertebra']}-{angle['lower_vertebra']}"
                    if curve == curve_type:
                        cobb_angle = angle
                        break
            else:
                # Use the primary curve
                for angle in exam["cobb_angles"]:
                    if angle["is_primary"]:
                        cobb_angle = angle
                        break
                
                # If no primary curve is marked, use the first one
                if not cobb_angle and exam["cobb_angles"]:
                    cobb_angle = exam["cobb_angles"][0]
            
            if cobb_angle:
                dates.append(datetime.datetime.strptime(exam["date"], "%Y-%m-%d"))
                angles.append(cobb_angle["angle"])
                curve_info.append(f"{cobb_angle['upper_vertebra']}-{cobb_angle['lower_vertebra']}")
        
        # Sort by date
        sorted_indices = np.argsort(dates)
        dates = [dates[i] for i in sorted_indices]
        angles = [angles[i] for i in sorted_indices]
        curve_info = [curve_info[i] for i in sorted_indices]
        
        return {
            "patient_id": patient_id,
            "patient_name": examinations[0]["patient_name"],
            "dates": dates,
            "angles": angles,
            "curve_info": curve_info
        }
    
    def calculate_progression_rate(self, progression_data):
        """Calculate the progression rate
        
        Args:
            progression_data: Dictionary with progression data
            
        Returns:
            Dictionary with progression rate information
        """
        if "error" in progression_data:
            return progression_data
        
        dates = progression_data["dates"]
        angles = progression_data["angles"]
        
        if len(dates) < 2:
            return {
                "error": "Need at least two examinations to calculate progression rate"
            }
        
        # Calculate time differences in days
        time_diffs = [(dates[i] - dates[0]).days / 365.25 for i in range(len(dates))]  # Convert to years
        
        # Calculate angle differences
        angle_diffs = [angles[i] - angles[0] for i in range(len(angles))]
        
        # Calculate progression rate (degrees per year)
        if time_diffs[-1] > 0:
            progression_rate = angle_diffs[-1] / time_diffs[-1]
        else:
            progression_rate = 0
        
        # Calculate risk category based on progression rate
        risk_category = "Low"
        if progression_rate > 5:
            risk_category = "High"
        elif progression_rate > 2:
            risk_category = "Moderate"
        
        return {
            "patient_id": progression_data["patient_id"],
            "patient_name": progression_data["patient_name"],
            "initial_angle": angles[0],
            "current_angle": angles[-1],
            "total_change": angles[-1] - angles[0],
            "time_period_years": time_diffs[-1],
            "progression_rate": progression_rate,
            "risk_category": risk_category
        }
    
    def generate_progression_chart(self, progression_data, output_path=None):
        """Generate a chart showing progression over time
        
        Args:
            progression_data: Dictionary with progression data
            output_path: Path to save the chart image
            
        Returns:
            PIL Image with the chart
        """
        if "error" in progression_data:
            # Create an error image
            img = Image.new('RGB', (800, 400), color='white')
            draw = ImageDraw.Draw(img)
            draw.text((400, 200), progression_data["error"], fill='red')
            
            if output_path:
                img.save(output_path)
            
            return img
        
        # Create a matplotlib figure
        fig = Figure(figsize=(10, 6), dpi=100)
        canvas = FigureCanvas(fig)
        ax = fig.add_subplot(111)
        
        # Plot the data
        dates = progression_data["dates"]
        angles = progression_data["angles"]
        
        ax.plot(dates, angles, 'o-', color='blue', linewidth=2, markersize=8)
        
        # Add a trend line
        if len(dates) > 1:
            z = np.polyfit(mdates.date2num(dates), angles, 1)
            p = np.poly1d(z)
            ax.plot(dates, p(mdates.date2num(dates)), "r--", alpha=0.8)
        
        # Format the chart
        ax.set_title(f"Scoliosis Progression for {progression_data['patient_name']}", fontsize=14)
        ax.set_xlabel("Date", fontsize=12)
        ax.set_ylabel("Cobb Angle (degrees)", fontsize=12)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Format the date axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        fig.autofmt_xdate()
        
        # Add data points labels
        for i, (date, angle, curve) in enumerate(zip(dates, angles, progression_data["curve_info"])):
            ax.annotate(f"{angle:.1f}° ({curve})", 
                      (date, angle),
                      textcoords="offset points",
                      xytext=(0, 10),
                      ha='center')
        
        # Add progression rate if there are multiple data points
        if len(dates) > 1:
            progression_rate = self.calculate_progression_rate(progression_data)
            rate_text = f"Progression Rate: {progression_rate['progression_rate']:.2f}°/year"
            risk_text = f"Risk Category: {progression_rate['risk_category']}"
            
            # Add text box with progression information
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax.text(0.05, 0.95, rate_text + "\n" + risk_text, transform=ax.transAxes,
                  fontsize=10, verticalalignment='top', bbox=props)
        
        # Adjust layout
        fig.tight_layout()
        
        # Convert to PIL Image
        canvas.draw()
        img = Image.frombytes('RGB', canvas.get_width_height(), canvas.tostring_rgb())
        
        # Save if output path is provided
        if output_path:
            img.save(output_path)
        
        return img
    
    def compare_xrays(self, image_path1, image_path2, output_path=None):
        """Compare two X-ray images side by side
        
        Args:
            image_path1: Path to the first X-ray image
            image_path2: Path to the second X-ray image
            output_path: Path to save the comparison image
            
        Returns:
            PIL Image with the comparison
        """
        # Load the images
        img1 = cv2.imread(image_path1)
        img2 = cv2.imread(image_path2)
        
        if img1 is None or img2 is None:
            raise ValueError("Could not load one or both images")
        
        # Resize to the same height
        height = min(img1.shape[0], img2.shape[0])
        width1 = int(img1.shape[1] * height / img1.shape[0])
        width2 = int(img2.shape[1] * height / img2.shape[0])
        
        img1_resized = cv2.resize(img1, (width1, height))
        img2_resized = cv2.resize(img2, (width2, height))
        
        # Create a side-by-side comparison
        comparison = np.zeros((height, width1 + width2 + 20, 3), dtype=np.uint8)
        comparison[:, :width1] = img1_resized
        comparison[:, width1+20:width1+20+width2] = img2_resized
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Initial", (10, 30), font, 1, (255, 255, 255), 2)
        cv2.putText(comparison, "Current", (width1+30, 30), font, 1, (255, 255, 255), 2)
        
        # Convert to PIL Image
        comparison_rgb = cv2.cvtColor(comparison, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(comparison_rgb)
        
        # Save if output path is provided
        if output_path:
            img.save(output_path)
        
        return img

def track_progression(patient_id, output_dir="progression_results"):
    """Track progression for a patient
    
    Args:
        patient_id: Patient ID
        output_dir: Directory to save the results
        
    Returns:
        Dictionary with progression tracking results
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize the tracker
    tracker = ProgressionTracker()
    
    # Get progression data
    progression_data = tracker.get_progression_data(patient_id)
    
    if "error" in progression_data:
        return progression_data
    
    # Calculate progression rate
    progression_rate = tracker.calculate_progression_rate(progression_data)
    
    # Generate progression chart
    chart_output = os.path.join(output_dir, f"patient_{patient_id}_progression_chart.png")
    chart_img = tracker.generate_progression_chart(progression_data, chart_output)
    
    # Get examinations to compare first and last X-rays
    examinations = tracker.get_patient_examinations(patient_id)
    
    if len(examinations) >= 2:
        # Compare first and last X-rays
        first_exam = examinations[-1]  # Oldest examination (they're sorted newest first)
        last_exam = examinations[0]    # Newest examination
        
        comparison_output = os.path.join(output_dir, f"patient_{patient_id}_xray_comparison.png")
        comparison_img = tracker.compare_xrays(
            first_exam["image_path"],
            last_exam["image_path"],
            comparison_output
        )
    
    # Return results
    results = {
        "patient_id": patient_id,
        "patient_name": progression_data["patient_name"],
        "progression_data": progression_data,
        "progression_rate": progression_rate,
        "output_files": {
            "progression_chart": chart_output
        }
    }
    
    if len(examinations) >= 2:
        results["output_files"]["xray_comparison"] = comparison_output
    
    return results

if __name__ == "__main__":
    # Test the module with a sample patient
    import sys
    
    if len(sys.argv) > 1:
        patient_id = int(sys.argv[1])
        results = track_progression(patient_id)
        
        if "error" in results:
            print(f"Error: {results['error']}")
        else:
            print(f"Progression tracking complete for {results['patient_name']}")
            print(f"Progression rate: {results['progression_rate']['progression_rate']:.2f}°/year")
            print(f"Risk category: {results['progression_rate']['risk_category']}")
    else:
        print("Please provide a patient ID as an argument.")
