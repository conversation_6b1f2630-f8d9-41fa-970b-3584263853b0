"""
Theme Manager Module
This module provides functionality to manage light and dark themes
"""

import tkinter as tk
from tkinter import ttk
import json
import os

# Define color schemes
LIGHT_THEME = {
    "primary_color": "#2c3e50",      # Dark blue-gray
    "secondary_color": "#3498db",    # Bright blue
    "accent_color": "#e74c3c",       # Red
    "bg_color": "#ecf0f1",           # Light gray
    "text_color": "#2c3e50",         # Dark blue-gray
    "highlight_color": "#2ecc71",    # Green
    "header_bg": "#2c3e50",          # Dark blue-gray
    "header_fg": "#ffffff",          # White
    "button_bg": "#3498db",          # Bright blue
    "button_fg": "#ffffff",          # White
    "frame_bg": "#ecf0f1",           # Light gray
    "entry_bg": "#ffffff",           # White
    "entry_fg": "#2c3e50",           # Dark blue-gray
    "label_bg": "#ecf0f1",           # Light gray
    "label_fg": "#2c3e50",           # Dark blue-gray
}

DARK_THEME = {
    "primary_color": "#1a1a2e",      # Very dark blue
    "secondary_color": "#0f3460",    # Dark blue
    "accent_color": "#e94560",       # Pinkish red
    "bg_color": "#16213e",           # Dark blue-gray
    "text_color": "#e6e6e6",         # Light gray
    "highlight_color": "#4ecca3",    # Teal
    "header_bg": "#1a1a2e",          # Very dark blue
    "header_fg": "#e6e6e6",          # Light gray
    "button_bg": "#0f3460",          # Dark blue
    "button_fg": "#e6e6e6",          # Light gray
    "frame_bg": "#16213e",           # Dark blue-gray
    "entry_bg": "#1f2833",           # Dark gray-blue
    "entry_fg": "#e6e6e6",           # Light gray
    "label_bg": "#16213e",           # Dark blue-gray
    "label_fg": "#e6e6e6",           # Light gray
}

class ThemeManager:
    """Manages application themes (light/dark mode)"""
    
    def __init__(self):
        """Initialize the theme manager"""
        self.config_file = "theme_config.json"
        self.current_theme = self._load_theme_preference()
        self.theme_colors = LIGHT_THEME if self.current_theme == "light" else DARK_THEME
        self.callbacks = []  # List of callbacks to call when theme changes
    
    def _load_theme_preference(self):
        """Load theme preference from config file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    config = json.load(f)
                    return config.get("theme", "light")
            except Exception as e:
                print(f"Error loading theme preference: {e}")
                return "light"
        else:
            return "light"
    
    def _save_theme_preference(self):
        """Save theme preference to config file"""
        try:
            config = {"theme": self.current_theme}
            with open(self.config_file, "w") as f:
                json.dump(config, f)
        except Exception as e:
            print(f"Error saving theme preference: {e}")
    
    def get_theme(self):
        """Get the current theme name"""
        return self.current_theme
    
    def get_color(self, color_name):
        """Get a color from the current theme"""
        return self.theme_colors.get(color_name, "#000000")
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        self.current_theme = "dark" if self.current_theme == "light" else "light"
        self.theme_colors = LIGHT_THEME if self.current_theme == "light" else DARK_THEME
        self._save_theme_preference()
        
        # Call all registered callbacks
        for callback in self.callbacks:
            callback()
        
        return self.current_theme
    
    def set_theme(self, theme_name):
        """Set the theme to a specific value"""
        if theme_name not in ["light", "dark"]:
            return False
        
        if theme_name != self.current_theme:
            self.current_theme = theme_name
            self.theme_colors = LIGHT_THEME if self.current_theme == "light" else DARK_THEME
            self._save_theme_preference()
            
            # Call all registered callbacks
            for callback in self.callbacks:
                callback()
        
        return True
    
    def register_callback(self, callback):
        """Register a callback to be called when the theme changes"""
        if callable(callback) and callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def apply_theme_to_ttk(self, root):
        """Apply the current theme to ttk widgets"""
        style = ttk.Style(root)
        style.theme_use('clam')  # Use the 'clam' theme as a base
        
        # Configure ttk styles based on the current theme
        style.configure('TButton', 
                      font=('Segoe UI', 12), 
                      background=self.get_color("button_bg"), 
                      foreground=self.get_color("button_fg"))
        
        style.configure('TLabel', 
                      font=('Segoe UI', 12), 
                      background=self.get_color("label_bg"), 
                      foreground=self.get_color("label_fg"))
        
        style.configure('TFrame', 
                      background=self.get_color("frame_bg"))
        
        style.configure('Header.TLabel', 
                      font=('Segoe UI', 24, 'bold'), 
                      background=self.get_color("header_bg"), 
                      foreground=self.get_color("header_fg"))
        
        style.configure('Subheader.TLabel', 
                      font=('Segoe UI', 16), 
                      background=self.get_color("label_bg"), 
                      foreground=self.get_color("label_fg"))
        
        style.configure('TNotebook', 
                      background=self.get_color("frame_bg"))
        
        style.configure('TNotebook.Tab', 
                      background=self.get_color("button_bg"),
                      foreground=self.get_color("button_fg"),
                      padding=[10, 2])
        
        style.map('TNotebook.Tab',
                background=[('selected', self.get_color("highlight_color"))],
                foreground=[('selected', self.get_color("text_color"))])
        
        style.configure('Treeview', 
                      background=self.get_color("entry_bg"),
                      foreground=self.get_color("entry_fg"),
                      fieldbackground=self.get_color("entry_bg"))
        
        style.map('Treeview',
                background=[('selected', self.get_color("highlight_color"))],
                foreground=[('selected', self.get_color("text_color"))])
    
    def apply_theme_to_widgets(self, widgets):
        """Apply the current theme to a list of tkinter widgets"""
        for widget in widgets:
            self._apply_theme_to_widget(widget)
    
    def _apply_theme_to_widget(self, widget):
        """Apply the current theme to a single widget"""
        if isinstance(widget, tk.Tk) or isinstance(widget, tk.Toplevel):
            widget.configure(background=self.get_color("bg_color"))
            self._apply_theme_to_children(widget)
        
        elif isinstance(widget, tk.Frame) or isinstance(widget, tk.LabelFrame):
            widget.configure(background=self.get_color("frame_bg"))
            self._apply_theme_to_children(widget)
        
        elif isinstance(widget, tk.Label):
            widget.configure(
                background=self.get_color("label_bg"),
                foreground=self.get_color("label_fg")
            )
        
        elif isinstance(widget, tk.Button):
            widget.configure(
                background=self.get_color("button_bg"),
                foreground=self.get_color("button_fg"),
                activebackground=self.get_color("highlight_color"),
                activeforeground=self.get_color("text_color")
            )
        
        elif isinstance(widget, tk.Entry) or isinstance(widget, tk.Text):
            widget.configure(
                background=self.get_color("entry_bg"),
                foreground=self.get_color("entry_fg"),
                insertbackground=self.get_color("text_color")  # Cursor color
            )
        
        elif isinstance(widget, tk.Canvas):
            widget.configure(background=self.get_color("frame_bg"))
    
    def _apply_theme_to_children(self, widget):
        """Apply theme to all children of a widget"""
        for child in widget.winfo_children():
            self._apply_theme_to_widget(child)

# Create a singleton instance
theme_manager = ThemeManager()
