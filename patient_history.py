import os
import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class PatientHistoryTracker:
    def __init__(self, root=None):
        """Initialize the patient history tracker"""
        self.root = root
        self.db_path = 'evaluation.db'
        self._setup_database()
        
    def _setup_database(self):
        """Set up the database tables if they don't exist"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create reports table if it doesn't exist (this should already exist)
            cursor.execute('''CREATE TABLE IF NOT EXISTS reports
                             (patient_id TEXT, patient_name TEXT, timestamp TEXT, 
                              diagnosis TEXT, image_path TEXT, report_path TEXT)''')
            
            # Create a new table for tracking measurements over time
            cursor.execute('''CREATE TABLE IF NOT EXISTS patient_measurements
                             (id INTEGER PRIMARY KEY AUTOINCREMENT,
                              patient_id TEXT,
                              timestamp TEXT,
                              cobb_angle REAL,
                              severity TEXT,
                              confidence_score REAL,
                              notes TEXT)''')
            
            # Create a new table for patient information
            cursor.execute('''CREATE TABLE IF NOT EXISTS patients
                             (patient_id TEXT PRIMARY KEY,
                              patient_name TEXT,
                              first_visit TEXT,
                              last_visit TEXT)''')
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error setting up database: {str(e)}")
    
    def add_measurement(self, patient_id, cobb_angle, severity=None, confidence_score=None, notes=None):
        """Add a new measurement for a patient"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get patient name if not in measurements table yet
            cursor.execute("SELECT patient_name FROM patients WHERE patient_id = ?", (patient_id,))
            patient_result = cursor.fetchone()
            
            if not patient_result:
                # Try to get from reports table
                cursor.execute("SELECT DISTINCT patient_name FROM reports WHERE patient_id = ?", (patient_id,))
                report_result = cursor.fetchone()
                
                if report_result:
                    # Add to patients table
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    cursor.execute('''INSERT INTO patients 
                                     (patient_id, patient_name, first_visit, last_visit) 
                                     VALUES (?, ?, ?, ?)''', 
                                   (patient_id, report_result[0], current_time, current_time))
            
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Update patient's last visit time
            cursor.execute('''UPDATE patients SET last_visit = ? WHERE patient_id = ?''',
                          (timestamp, patient_id))
            
            cursor.execute('''INSERT INTO patient_measurements
                             (patient_id, timestamp, cobb_angle, severity, confidence_score, notes)
                             VALUES (?, ?, ?, ?, ?, ?)''',
                           (patient_id, timestamp, cobb_angle, severity, confidence_score, notes))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error adding measurement: {str(e)}")
            return False
    
    def get_patient_history(self, patient_id):
        """Get all measurements for a specific patient"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''SELECT timestamp, cobb_angle, severity, confidence_score, notes
                             FROM patient_measurements
                             WHERE patient_id = ?
                             ORDER BY timestamp ASC''', (patient_id,))
            
            measurements = cursor.fetchall()
            
            # Get patient name
            cursor.execute('''SELECT DISTINCT patient_name FROM reports
                             WHERE patient_id = ?''', (patient_id,))
            patient_name = cursor.fetchone()
            
            conn.close()
            
            return {
                'patient_id': patient_id,
                'patient_name': patient_name[0] if patient_name else "Unknown",
                'measurements': measurements
            }
        except Exception as e:
            print(f"Error getting patient history: {str(e)}")
            return None
    
    def get_all_patients(self):
        """Get a list of all patients with measurements"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''SELECT DISTINCT r.patient_id, r.patient_name, 
                             COUNT(m.id) as measurement_count,
                             MAX(m.timestamp) as last_visit
                             FROM reports r
                             LEFT JOIN patient_measurements m ON r.patient_id = m.patient_id
                             GROUP BY r.patient_id
                             ORDER BY r.patient_name''')
            
            patients = cursor.fetchall()
            conn.close()
            
            return patients
        except Exception as e:
            print(f"Error getting patients: {str(e)}")
            return []
    
    def show_history_window(self):
        """Display the patient history tracking window"""
        if not self.root:
            self.root = tk.Toplevel()
            self.root.title("Patient History Tracker")
            self.root.geometry("1000x700")
            self.root.configure(bg="#f0f0f0")
        
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create left panel for patient list
        left_panel = ttk.Frame(main_frame, width=300)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        
        # Create right panel for patient details and graphs
        right_panel = ttk.Frame(main_frame)
        right_panel.pack(side="right", fill="both", expand=True)
        
        # Patient list
        ttk.Label(left_panel, text="Patients", font=("Arial", 14, "bold")).pack(pady=(0, 10), anchor="w")
        
        # Create treeview for patient list
        columns = ("ID", "Name", "Visits", "Last Visit")
        patient_tree = ttk.Treeview(left_panel, columns=columns, show="headings", height=20)
        
        # Define columns
        patient_tree.column("ID", width=80, anchor="center")
        patient_tree.column("Name", width=150, anchor="w")
        patient_tree.column("Visits", width=50, anchor="center")
        patient_tree.column("Last Visit", width=100, anchor="center")
        
        # Define headings
        patient_tree.heading("ID", text="Patient ID")
        patient_tree.heading("Name", text="Name")
        patient_tree.heading("Visits", text="Visits")
        patient_tree.heading("Last Visit", text="Last Visit")
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(left_panel, orient="vertical", command=patient_tree.yview)
        patient_tree.configure(yscrollcommand=scrollbar.set)
        
        patient_tree.pack(side="left", fill="y")
        scrollbar.pack(side="right", fill="y")
        
        # Load patients into treeview
        patients = self.get_all_patients()
        for patient in patients:
            patient_id, name, visit_count, last_visit = patient
            # Format the last visit date
            if last_visit:
                last_visit_date = datetime.strptime(last_visit, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
            else:
                last_visit_date = "N/A"
                visit_count = 0
            
            patient_tree.insert("", "end", values=(patient_id, name, visit_count, last_visit_date))
        
        # Right panel - Patient details
        details_frame = ttk.LabelFrame(right_panel, text="Patient Details")
        details_frame.pack(fill="x", pady=(0, 10))
        
        # Patient info
        patient_info_frame = ttk.Frame(details_frame)
        patient_info_frame.pack(fill="x", padx=10, pady=10)
        
        ttk.Label(patient_info_frame, text="Patient ID:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        patient_id_var = tk.StringVar()
        ttk.Label(patient_info_frame, textvariable=patient_id_var).grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(patient_info_frame, text="Patient Name:").grid(row=0, column=2, sticky="w", padx=5, pady=2)
        patient_name_var = tk.StringVar()
        ttk.Label(patient_info_frame, textvariable=patient_name_var).grid(row=0, column=3, sticky="w", padx=5, pady=2)
        
        ttk.Label(patient_info_frame, text="Total Visits:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        visits_var = tk.StringVar()
        ttk.Label(patient_info_frame, textvariable=visits_var).grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(patient_info_frame, text="Last Visit:").grid(row=1, column=2, sticky="w", padx=5, pady=2)
        last_visit_var = tk.StringVar()
        ttk.Label(patient_info_frame, textvariable=last_visit_var).grid(row=1, column=3, sticky="w", padx=5, pady=2)
        
        # Measurements history
        history_frame = ttk.LabelFrame(right_panel, text="Measurement History")
        history_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Create treeview for measurements
        columns = ("Date", "Cobb Angle", "Severity", "Confidence", "Notes")
        history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=8)
        
        # Define columns
        history_tree.column("Date", width=150, anchor="center")
        history_tree.column("Cobb Angle", width=100, anchor="center")
        history_tree.column("Severity", width=100, anchor="center")
        history_tree.column("Confidence", width=100, anchor="center")
        history_tree.column("Notes", width=300, anchor="w")
        
        # Define headings
        history_tree.heading("Date", text="Date")
        history_tree.heading("Cobb Angle", text="Cobb Angle (°)")
        history_tree.heading("Severity", text="Severity")
        history_tree.heading("Confidence", text="Confidence (%)")
        history_tree.heading("Notes", text="Notes")
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=history_tree.yview)
        history_tree.configure(yscrollcommand=scrollbar.set)
        
        history_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)
        
        # Graph frame
        graph_frame = ttk.LabelFrame(right_panel, text="Progress Graph")
        graph_frame.pack(fill="both", expand=True)
        
        # Create matplotlib figure
        fig = plt.Figure(figsize=(8, 4), dpi=100)
        ax = fig.add_subplot(111)
        canvas = FigureCanvasTkAgg(fig, graph_frame)
        canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
        
        # Function to update patient details and graph
        def update_patient_details(event):
            selected_item = patient_tree.selection()
            if not selected_item:
                return
            
            # Get patient ID
            patient_id = patient_tree.item(selected_item[0], "values")[0]
            
            # Get patient history
            history = self.get_patient_history(patient_id)
            if not history:
                return
            
            # Update patient info
            patient_id_var.set(history['patient_id'])
            patient_name_var.set(history['patient_name'])
            visits_var.set(str(len(history['measurements'])))
            if history['measurements']:
                last_visit_var.set(datetime.strptime(history['measurements'][-1][0], "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d"))
            else:
                last_visit_var.set("N/A")
            
            # Clear history tree
            for item in history_tree.get_children():
                history_tree.delete(item)
            
            # Add measurements to history tree
            for measurement in history['measurements']:
                timestamp, cobb_angle, severity, confidence, notes = measurement
                date = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
                cobb_angle_str = f"{cobb_angle:.1f}°" if cobb_angle is not None else "N/A"
                confidence_str = f"{confidence:.1f}%" if confidence is not None else "N/A"
                
                history_tree.insert("", "end", values=(date, cobb_angle_str, severity, confidence_str, notes))
            
            # Update graph
            ax.clear()
            
            if history['measurements']:
                dates = [datetime.strptime(m[0], "%Y-%m-%d %H:%M:%S") for m in history['measurements']]
                cobb_angles = [m[1] if m[1] is not None else 0 for m in history['measurements']]
                
                # Plot data
                ax.plot(dates, cobb_angles, 'o-', color='#0277bd', linewidth=2, markersize=8)
                
                # Add reference lines for severity levels
                ax.axhline(y=10, color='#81c784', linestyle='--', alpha=0.5, label='Mild')
                ax.axhline(y=25, color='#ffb74d', linestyle='--', alpha=0.5, label='Moderate')
                ax.axhline(y=40, color='#e57373', linestyle='--', alpha=0.5, label='Severe')
                
                # Format the graph
                ax.set_xlabel('Date')
                ax.set_ylabel('Cobb Angle (°)')
                ax.set_title(f'Scoliosis Progression - {history["patient_name"]}')
                ax.grid(True, linestyle='--', alpha=0.7)
                ax.legend()
                
                # Format x-axis dates
                fig.autofmt_xdate()
                
                # Set y-axis limits
                max_angle = max(cobb_angles) if cobb_angles else 50
                ax.set_ylim(0, max(50, max_angle * 1.2))
            else:
                ax.text(0.5, 0.5, 'No measurement data available', 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes)
            
            canvas.draw()
        
        # Bind selection event
        patient_tree.bind("<<TreeviewSelect>>", update_patient_details)
        
        # Add new measurement button
        def add_new_measurement():
            selected_item = patient_tree.selection()
            if not selected_item:
                messagebox.showinfo("Select Patient", "Please select a patient first")
                return
            
            # Get patient ID and name
            patient_id = patient_tree.item(selected_item[0], "values")[0]
            patient_name = patient_tree.item(selected_item[0], "values")[1]
            
            # Create add measurement dialog
            add_window = tk.Toplevel(self.root)
            add_window.title(f"Add Measurement for {patient_name}")
            add_window.geometry("400x300")
            add_window.resizable(False, False)
            add_window.transient(self.root)
            add_window.grab_set()
            
            # Add form fields
            ttk.Label(add_window, text="Cobb Angle (°):").grid(row=0, column=0, padx=10, pady=10, sticky="w")
            cobb_angle_var = tk.DoubleVar(value=0.0)
            ttk.Spinbox(add_window, from_=0, to=90, increment=0.5, textvariable=cobb_angle_var, width=10).grid(row=0, column=1, padx=10, pady=10, sticky="w")
            
            ttk.Label(add_window, text="Severity:").grid(row=1, column=0, padx=10, pady=10, sticky="w")
            severity_var = tk.StringVar(value="Mild")
            ttk.Combobox(add_window, textvariable=severity_var, values=["Mild", "Moderate", "Severe"], state="readonly", width=10).grid(row=1, column=1, padx=10, pady=10, sticky="w")
            
            ttk.Label(add_window, text="Confidence Score (%):").grid(row=2, column=0, padx=10, pady=10, sticky="w")
            confidence_var = tk.DoubleVar(value=85.0)
            ttk.Spinbox(add_window, from_=0, to=100, increment=0.1, textvariable=confidence_var, width=10).grid(row=2, column=1, padx=10, pady=10, sticky="w")
            
            ttk.Label(add_window, text="Notes:").grid(row=3, column=0, padx=10, pady=10, sticky="nw")
            notes_text = tk.Text(add_window, width=30, height=5)
            notes_text.grid(row=3, column=1, padx=10, pady=10, sticky="w")
            
            def save_measurement():
                try:
                    cobb_angle = cobb_angle_var.get()
                    severity = severity_var.get()
                    confidence = confidence_var.get()
                    notes = notes_text.get("1.0", "end-1c")
                    
                    # Add measurement to database
                    success = self.add_measurement(patient_id, cobb_angle, severity, confidence, notes)
                    
                    if success:
                        messagebox.showinfo("Success", "Measurement added successfully")
                        add_window.destroy()
                        
                        # Refresh patient list
                        for item in patient_tree.get_children():
                            patient_tree.delete(item)
                        
                        patients = self.get_all_patients()
                        for patient in patients:
                            patient_id, name, visit_count, last_visit = patient
                            if last_visit:
                                last_visit_date = datetime.strptime(last_visit, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
                            else:
                                last_visit_date = "N/A"
                                visit_count = 0
                            
                            patient_tree.insert("", "end", values=(patient_id, name, visit_count, last_visit_date))
                        
                        # Reselect the patient
                        for item in patient_tree.get_children():
                            if patient_tree.item(item, "values")[0] == patient_id:
                                patient_tree.selection_set(item)
                                patient_tree.see(item)
                                update_patient_details(None)
                                break
                    else:
                        messagebox.showerror("Error", "Failed to add measurement")
                except Exception as e:
                    messagebox.showerror("Error", f"An error occurred: {str(e)}")
            
            # Add buttons
            ttk.Button(add_window, text="Save", command=save_measurement).grid(row=4, column=0, columnspan=2, padx=10, pady=20)
        
        # Add buttons frame
        buttons_frame = ttk.Frame(left_panel)
        buttons_frame.pack(fill="x", pady=10)
        
        ttk.Button(buttons_frame, text="Add Measurement", command=add_new_measurement).pack(side="left", padx=5)
        
        # Export button to save graph as image
        def export_graph():
            selected_item = patient_tree.selection()
            if not selected_item:
                messagebox.showinfo("Select Patient", "Please select a patient first")
                return
            
            patient_id = patient_tree.item(selected_item[0], "values")[0]
            patient_name = patient_tree.item(selected_item[0], "values")[1]
            
            # Create reports directory if it doesn't exist
