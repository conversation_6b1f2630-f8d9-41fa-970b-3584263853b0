import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import subprocess
import tkinter.simpledialog
import shutil
from tkinter import filedialog

class ReportViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Scoliosis Detection - Report Viewer")
        self.root.geometry("1000x600")
        self.root.configure(bg="#f0f0f0")

        # Title
        title = tk.Label(root, text="Patient Reports", font=("Times New Roman", 24, "bold"),
                        bg="#f0f0f0", fg="#333333")
        title.pack(pady=20)

        # Create frame for treeview
        frame = tk.Frame(root, bg="#f0f0f0")
        frame.pack(pady=10, padx=20, fill="both", expand=True)

        # Create scrollbar
        scrollbar = ttk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Create treeview
        self.tree = ttk.Treeview(frame, columns=("ID", "Name", "Date", "Diagnosis"),
                                show="headings", yscrollcommand=scrollbar.set)

        # Define columns
        self.tree.column("ID", width=100, anchor="center")
        self.tree.column("Name", width=200, anchor="w")
        self.tree.column("Date", width=150, anchor="center")
        self.tree.column("Diagnosis", width=400, anchor="w")

        # Define headings
        self.tree.heading("ID", text="Patient ID")
        self.tree.heading("Name", text="Patient Name")
        self.tree.heading("Date", text="Date")
        self.tree.heading("Diagnosis", text="Diagnosis")

        # Pack treeview
        self.tree.pack(fill="both", expand=True)

        # Configure scrollbar
        scrollbar.config(command=self.tree.yview)

        # Bind double-click event
        self.tree.bind("<Double-1>", self.open_report)

        # Create button frame
        btn_frame = tk.Frame(root, bg="#f0f0f0")
        btn_frame.pack(pady=20, padx=20)

        # Create buttons
        self.view_btn = tk.Button(btn_frame, text="View Report", command=lambda: self.open_report(None),
                                font=("Times New Roman", 12), bg="#4CAF50", fg="white",
                                width=15, height=1)
        self.view_btn.grid(row=0, column=0, padx=10)

        self.html_btn = tk.Button(btn_frame, text="View as HTML", command=self.convert_to_html,
                                font=("Times New Roman", 12), bg="#9C27B0", fg="white",
                                width=15, height=1)
        self.html_btn.grid(row=0, column=1, padx=10)

        self.download_btn = tk.Button(btn_frame, text="Download Report", command=self.download_report,
                                    font=("Times New Roman", 12), bg="#2196F3", fg="white",
                                    width=15, height=1)
        self.download_btn.grid(row=0, column=2, padx=10)

        self.email_btn = tk.Button(btn_frame, text="Email Report", command=self.email_report,
                                 font=("Times New Roman", 12), bg="#FF9800", fg="white",
                                 width=15, height=1)
        self.email_btn.grid(row=0, column=3, padx=10)

        self.delete_btn = tk.Button(btn_frame, text="Delete Report", command=self.delete_report,
                                  font=("Times New Roman", 12), bg="#F44336", fg="white",
                                  width=15, height=1)
        self.delete_btn.grid(row=0, column=4, padx=10)

        # Back button
        self.back_btn = tk.Button(btn_frame, text="Back to Main", command=self.back_to_main,
                                font=("Times New Roman", 12), bg="#607D8B", fg="white",
                                width=15, height=1)
        self.back_btn.grid(row=1, column=2, pady=10)

        # Load reports
        self.load_reports()

    def load_reports(self):
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # Connect to database
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute('''CREATE TABLE IF NOT EXISTS reports
                             (patient_id TEXT, patient_name TEXT, timestamp TEXT,
                              diagnosis TEXT, image_path TEXT, report_path TEXT)''')

            # Get all reports
            cursor.execute("SELECT patient_id, patient_name, timestamp, diagnosis, report_path FROM reports ORDER BY timestamp DESC")
            reports = cursor.fetchall()

            # Insert reports into treeview
            for report in reports:
                self.tree.insert("", "end", values=(report[0], report[1], report[2], report[3]),
                               tags=(report[4],))

            conn.close()

            # Show message if no reports found
            if not reports:
                messagebox.showinfo("No Reports", "No reports found in the database")

        except Exception as e:
            messagebox.showerror("Database Error", f"Error loading reports: {str(e)}")

    def open_report(self, event):
        # Get selected item
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Report", "Please select a report to open")
            return

        # Get report path
        report_path = self.tree.item(selected_item[0], "tags")[0]

        # Check if file exists
        if not os.path.exists(report_path):
            messagebox.showerror("Error", "Report file not found")
            return

        # Check if HTML version exists
        html_path = report_path.replace('.pdf', '.html')

        if os.path.exists(html_path):
            # Open HTML file in browser
            import webbrowser
            webbrowser.open('file://' + os.path.realpath(html_path))
        else:
            # Open PDF file
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(report_path)
                else:  # macOS and Linux
                    subprocess.call(('xdg-open', report_path))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open report: {str(e)}")

    def download_report(self):
        # Get selected item
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Report", "Please select a report to download")
            return

        # Get report path and patient name
        report_path = self.tree.item(selected_item[0], "tags")[0]
        patient_name = self.tree.item(selected_item[0], "values")[1]
        patient_id = self.tree.item(selected_item[0], "values")[0]

        # Check if file exists
        if not os.path.exists(report_path):
            messagebox.showerror("Error", "Report file not found")
            return

        # Ask for download location
        download_path = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")],
            initialfile=f"{patient_id}_{patient_name.replace(' ', '_')}.pdf"
        )

        if not download_path:
            return  # User cancelled

        try:
            # Copy file to download location
            shutil.copy2(report_path, download_path)
            messagebox.showinfo("Success", f"Report downloaded to {download_path}")
        except Exception as e:
            messagebox.showerror("Download Error", f"Failed to download report: {str(e)}")

    def email_report(self):
        # Get selected item
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Report", "Please select a report to email")
            return

        # Get report path and patient name
        report_path = self.tree.item(selected_item[0], "tags")[0]
        patient_name = self.tree.item(selected_item[0], "values")[1]

        # Check if file exists
        if not os.path.exists(report_path):
            messagebox.showerror("Error", "Report file not found")
            return

        try:
            # Import ReportGenerator and EmailDialog
            from report_system import ReportGenerator
            from email_dialog import EmailDialog

            # Create ReportGenerator instance with existing report
            report_gen = ReportGenerator(patient_name)
            report_gen.filename = report_path

            # Show email dialog
            email_dialog = EmailDialog(self.root, report_gen)
            result = email_dialog.show()

            # Show result message if needed
            if result["success"]:
                messagebox.showinfo("Success", "Email sent successfully!")
        except Exception as e:
            messagebox.showerror("Email Error", f"Failed to send email: {str(e)}")

    def delete_report(self):
        # Get selected item
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("Select Report", "Please select a report to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this report?"):
            return

        try:
            # Get report path and patient ID
            report_path = self.tree.item(selected_item[0], "tags")[0]
            patient_id = self.tree.item(selected_item[0], "values")[0]

            # Delete from database
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()
            cursor.execute("DELETE FROM reports WHERE patient_id = ? AND report_path = ?",
                         (patient_id, report_path))
            conn.commit()
            conn.close()

            # Delete file
            if os.path.exists(report_path):
                try:
                    os.remove(report_path)
                except Exception as e:
                    messagebox.showwarning("Warning", f"Could not delete file: {str(e)}")

            # Refresh list
            self.load_reports()
            messagebox.showinfo("Success", "Report deleted successfully")
        except Exception as e:
            messagebox.showerror("Delete Error", f"Failed to delete report: {str(e)}")

    def back_to_main(self):
        self.root.destroy()
        from subprocess import call
        call(["python", "GUI_Master_New.py"])

if __name__ == "__main__":
    root = tk.Tk()
    app = ReportViewer(root)
    root.mainloop()





