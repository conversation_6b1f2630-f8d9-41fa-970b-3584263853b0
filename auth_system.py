import os
import json
import hashlib
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageFilter, ImageEnhance
import subprocess
import sys
import sqlite3
import re

# Define color scheme (matching the main application)
PRIMARY_COLOR = "#2c3e50"  # Dark blue-gray
SECONDARY_COLOR = "#3498db"  # Bright blue
ACCENT_COLOR = "#e74c3c"  # Red
BG_COLOR = "#ecf0f1"  # Light gray
TEXT_COLOR = "#2c3e50"  # Dark blue-gray
HIGHLIGHT_COLOR = "#2ecc71"  # Green

# User database file for JSON-based authentication
USER_DB_FILE = "users.json"

class AuthSystem:
    def __init__(self, root):
        """Initialize the authentication system"""
        self.root = root
        self.root.title("Scoliosis Detection System - Authentication")
        self.root.configure(bg=BG_COLOR)

        # Get screen dimensions
        self.screen_width = self.root.winfo_screenwidth()
        self.screen_height = self.root.winfo_screenheight()

        # Set a reasonable minimum size
        self.root.minsize(800, 600)

        # Configure window size and position (centered)
        window_width = int(self.screen_width * 0.8)
        window_height = int(self.screen_height * 0.8)
        x_position = (self.screen_width - window_width) // 2
        y_position = (self.screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        self.root.resizable(True, True)

        # Start in maximized state
        self.is_fullscreen = False
        self.root.state('zoomed')  # Maximize the window

        # Bind F11 to toggle fullscreen and Escape to exit fullscreen
        self.root.bind("<F11>", self.toggle_fullscreen)
        self.root.bind("<Escape>", self.end_fullscreen)

        # Create window control buttons
        self.create_window_controls()

        # Update the window to ensure all measurements are correct
        self.root.update_idletasks()

        # Create styles for widgets
        self.create_styles()

        # Load user database
        self.load_users()

        # Create the main container frame
        self.main_container = ttk.Frame(self.root, style='TFrame')
        self.main_container.pack(fill='both', expand=True)

        # Create frames for login and registration
        self.login_frame = ttk.Frame(self.main_container, style='TFrame')
        self.register_frame = ttk.Frame(self.main_container, style='TFrame')

        # Initialize variables for login
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()

        # Initialize variables for registration
        self.fullname_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.reg_username_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.gender_var = tk.IntVar()
        self.age_var = tk.StringVar()
        self.reg_password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()

        # Create the login and registration frames
        self.create_login_frame()
        self.create_register_frame()

        # Show the login frame by default
        self.show_login()

    def toggle_fullscreen(self, event=None):
        """Toggle between fullscreen and windowed mode"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)
        return "break"  # Prevent default behavior

    def end_fullscreen(self, event=None):
        """Exit fullscreen mode"""
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)
        return "break"  # Prevent default behavior

    def create_styles(self):
        """Create styles for the UI elements"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure basic styles
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('TEntry', font=('Segoe UI', 10))

        # Custom button styles
        self.style.configure('Auth.TButton',
                           font=('Segoe UI', 12, 'bold'),
                           background=SECONDARY_COLOR,
                           foreground='white')

        self.style.configure('Cancel.TButton',
                           font=('Segoe UI', 12),
                           background=BG_COLOR,
                           foreground=TEXT_COLOR)

    def load_users(self):
        """Load user data from JSON file"""
        self.users = {}

        # First try to load from JSON file
        if os.path.exists(USER_DB_FILE):
            try:
                with open(USER_DB_FILE, 'r') as f:
                    self.users = json.load(f)
            except Exception as e:
                print(f"Error loading user database from JSON: {e}")

        # If no users found in JSON, try to load from SQLite
        if not self.users:
            try:
                # Connect to SQLite database
                conn = sqlite3.connect('evaluation.db')
                cursor = conn.cursor()

                # Create tables if they don't exist (for backward compatibility)
                cursor.execute("CREATE TABLE IF NOT EXISTS admin_registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")
                cursor.execute("CREATE TABLE IF NOT EXISTS registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")

                # Get users from admin_registration table
                cursor.execute("SELECT Fullname, username, password FROM admin_registration")
                admin_users = cursor.fetchall()

                # Get users from registration table
                cursor.execute("SELECT Fullname, username, password FROM registration")
                regular_users = cursor.fetchall()

                # Convert SQLite users to JSON format
                for fullname, username, password in admin_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "admin"
                        }

                for fullname, username, password in regular_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "user"
                        }

                # Save the converted users to JSON
                if self.users:
                    self.save_users()

                conn.close()
            except Exception as e:
                print(f"Error loading users from SQLite: {e}")

        # If still no users, create default admin
        if not self.users:
            # Create default admin user if no users exist
            admin_password = self.hash_password("admin123")
            self.users = {
                "admin": {
                    "password": admin_password,
                    "name": "Administrator",
                    "role": "admin"
                }
            }
            self.save_users()

    def save_users(self):
        """Save user data to JSON file"""
        try:
            with open(USER_DB_FILE, 'w') as f:
                json.dump(self.users, f, indent=4)
        except Exception as e:
            print(f"Error saving user database: {e}")

    def hash_password(self, password):
        """Hash a password using SHA-256"""
        if not password:
            return ""
        return hashlib.sha256(str(password).encode()).hexdigest()

    def show_login(self):
        """Show the login frame"""
        self.register_frame.pack_forget()
        self.login_frame.pack(fill='both', expand=True)

        # Reset login fields
        self.username_var.set("")
        self.password_var.set("")

        # Set focus to the first entry field we can find
        self.root.update()  # Make sure UI is updated
        for widget in self.login_frame.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Frame) and child.winfo_children():
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Entry):
                                grandchild.focus_set()
                                return

    def show_register(self):
        """Show the registration frame"""
        self.login_frame.pack_forget()
        self.register_frame.pack(fill='both', expand=True)

        # Reset registration fields
        self.fullname_var.set("")
        self.address_var.set("")
        self.reg_username_var.set("")
        self.email_var.set("")
        self.phone_var.set("")
        self.gender_var.set(0)
        self.age_var.set("")
        self.reg_password_var.set("")
        self.confirm_password_var.set("")

        # Set focus to the first entry field we can find
        self.root.update()  # Make sure UI is updated
        for widget in self.register_frame.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Frame) and child.winfo_children():
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Entry):
                                grandchild.focus_set()
                                return

    def create_login_frame(self):
        """Create the login frame with all its components"""
        # Create a two-column layout
        main_frame = ttk.Frame(self.login_frame, style='TFrame')
        main_frame.pack(fill='both', expand=True)

        # Left column - Logo/Image - width is 40% of screen width
        left_width = int(self.screen_width * 0.4)
        left_frame = ttk.Frame(main_frame, style='TFrame', width=left_width)
        left_frame.pack(side='left', fill='both')
        left_frame.pack_propagate(False)  # Maintain width

        # Create decorative background for login
        self.create_decorative_background(left_frame, "Welcome Back", "Sign in to continue to the Scoliosis Detection System")

        # Add a resize handler to adjust UI elements when window size changes
        self.root.bind("<Configure>", self.on_window_resize)

        # Right column - Login form
        right_frame = ttk.Frame(main_frame, style='TFrame')
        right_frame.pack(side='right', fill='both', expand=True)

        # Login form container (centered)
        login_container = ttk.Frame(right_frame, style='TFrame')
        login_container.place(relx=0.5, rely=0.5, anchor='center')

        # Login form title
        title_label = ttk.Label(login_container,
                              text="Welcome Back",
                              font=('Segoe UI', 24, 'bold'),
                              foreground=PRIMARY_COLOR)
        title_label.pack(pady=(0, 5))

        # Login form subtitle
        subtitle_label = ttk.Label(login_container,
                                 text="Sign in to continue to the Scoliosis Detection System",
                                 font=('Segoe UI', 10),
                                 foreground=TEXT_COLOR)
        subtitle_label.pack(pady=(0, 30))

        # Username field
        username_frame = ttk.Frame(login_container, style='TFrame')
        username_frame.pack(fill='x', pady=5)

        username_label = ttk.Label(username_frame,
                                 text="Username",
                                 font=('Segoe UI', 10, 'bold'),
                                 foreground=TEXT_COLOR)
        username_label.pack(anchor='w', pady=(0, 5))

        username_entry = ttk.Entry(username_frame,
                                 textvariable=self.username_var,
                                 font=('Segoe UI', 12),
                                 width=30,
                                 name="username_entry")
        username_entry.pack(ipady=5)

        # Password field
        password_frame = ttk.Frame(login_container, style='TFrame')
        password_frame.pack(fill='x', pady=15)

        password_label = ttk.Label(password_frame,
                                 text="Password",
                                 font=('Segoe UI', 10, 'bold'),
                                 foreground=TEXT_COLOR)
        password_label.pack(anchor='w', pady=(0, 5))

        password_entry = ttk.Entry(password_frame,
                                 textvariable=self.password_var,
                                 font=('Segoe UI', 12),
                                 width=30,
                                 show="•")
        password_entry.pack(ipady=5)

        # Remember me and forgot password row
        options_frame = ttk.Frame(login_container, style='TFrame')
        options_frame.pack(fill='x', pady=10)

        remember_check = ttk.Checkbutton(options_frame,
                                        text="Remember me",
                                        variable=self.remember_var)
        remember_check.pack(side='left')

        forgot_btn = ttk.Label(options_frame,
                             text="Forgot password?",
                             foreground=SECONDARY_COLOR,
                             cursor="hand2")
        forgot_btn.pack(side='right')
        forgot_btn.bind("<Button-1>", self.forgot_password)

        # Login button
        login_btn = ttk.Button(login_container,
                             text="Sign In",
                             command=self.login,
                             style='Auth.TButton',
                             width=30)
        login_btn.pack(pady=20, ipady=8)

        # Register link
        register_frame = ttk.Frame(login_container, style='TFrame')
        register_frame.pack(pady=10)

        register_label = ttk.Label(register_frame,
                                 text="Don't have an account? ",
                                 foreground=TEXT_COLOR)
        register_label.pack(side='left')

        register_link = ttk.Label(register_frame,
                                text="Register",
                                foreground=SECONDARY_COLOR,
                                cursor="hand2")
        register_link.pack(side='left')
        register_link.bind("<Button-1>", lambda e: self.show_register())

        # Bind Enter key to login
        self.login_frame.bind('<Return>', lambda event: self.login())

    def create_register_frame(self):
        """Create the registration frame with all its components"""
        # Create a two-column layout
        main_frame = ttk.Frame(self.register_frame, style='TFrame')
        main_frame.pack(fill='both', expand=True)

        # Left column - Logo/Image - width is 40% of screen width
        left_width = int(self.screen_width * 0.4)
        left_frame = ttk.Frame(main_frame, style='TFrame', width=left_width)
        left_frame.pack(side='left', fill='both')
        left_frame.pack_propagate(False)  # Maintain width

        # Create decorative background for registration
        self.create_decorative_background(left_frame, "Create New Account", "Join our platform to access advanced scoliosis detection tools")

        # Right column - Registration form
        right_frame = ttk.Frame(main_frame, style='TFrame')
        right_frame.pack(side='right', fill='both', expand=True)

        # Registration form container (centered)
        registration_container = ttk.Frame(right_frame, style='TFrame')
        registration_container.place(relx=0.5, rely=0.5, anchor='center')

        # Registration form title
        title_label = ttk.Label(registration_container,
                              text="Create New Account",
                              font=('Segoe UI', 24, 'bold'),
                              foreground=PRIMARY_COLOR)
        title_label.pack(pady=(0, 5))

        # Registration form subtitle
        subtitle_label = ttk.Label(registration_container,
                                 text="Please fill in the information below",
                                 font=('Segoe UI', 10),
                                 foreground=TEXT_COLOR)
        subtitle_label.pack(pady=(0, 20))

        # Create a frame for the form fields
        form_frame = ttk.Frame(registration_container, style='TFrame')
        form_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Full Name field
        self.create_form_field(form_frame, "Full Name:", self.fullname_var, 0, "fullname_entry")

        # Address field
        self.create_form_field(form_frame, "Address:", self.address_var, 1)

        # Email field
        self.create_form_field(form_frame, "Email:", self.email_var, 2)

        # Phone field
        self.create_form_field(form_frame, "Phone:", self.phone_var, 3)

        # Gender field
        gender_label = ttk.Label(form_frame, text="Gender:", font=('Segoe UI', 10, 'bold'))
        gender_label.grid(row=4, column=0, sticky='w', padx=5, pady=10)

        gender_frame = ttk.Frame(form_frame, style='TFrame')
        gender_frame.grid(row=4, column=1, sticky='w', padx=5, pady=10)

        ttk.Radiobutton(gender_frame, text="Male", variable=self.gender_var, value=1).pack(side='left', padx=10)
        ttk.Radiobutton(gender_frame, text="Female", variable=self.gender_var, value=2).pack(side='left', padx=10)

        # Age field
        self.create_form_field(form_frame, "Age:", self.age_var, 5)

        # Username field
        self.create_form_field(form_frame, "Username:", self.reg_username_var, 6)

        # Password field
        password_label = ttk.Label(form_frame, text="Password:", font=('Segoe UI', 10, 'bold'))
        password_label.grid(row=7, column=0, sticky='w', padx=5, pady=10)

        password_entry = ttk.Entry(form_frame, textvariable=self.reg_password_var, show="•", width=30)
        password_entry.grid(row=7, column=1, sticky='w', padx=5, pady=10)

        # Password requirements
        password_req = ttk.Label(form_frame,
                               text="Password must contain at least 1 uppercase letter, 1 number, and 1 special character",
                               font=('Segoe UI', 8),
                               foreground='gray')
        password_req.grid(row=8, column=1, sticky='w', padx=5)

        # Confirm Password field
        confirm_label = ttk.Label(form_frame, text="Confirm Password:", font=('Segoe UI', 10, 'bold'))
        confirm_label.grid(row=9, column=0, sticky='w', padx=5, pady=10)

        confirm_entry = ttk.Entry(form_frame, textvariable=self.confirm_password_var, show="•", width=30)
        confirm_entry.grid(row=9, column=1, sticky='w', padx=5, pady=10)

        # Buttons frame
        buttons_frame = ttk.Frame(registration_container, style='TFrame')
        buttons_frame.pack(fill='x', pady=20)

        # Register button
        register_btn = ttk.Button(buttons_frame,
                                text="Register",
                                command=self.register,
                                style='Auth.TButton',
                                width=20)
        register_btn.pack(side='left', padx=10, ipady=8)

        # Cancel button
        cancel_btn = ttk.Button(buttons_frame,
                              text="Cancel",
                              command=self.show_login,
                              style='Cancel.TButton',
                              width=20)
        cancel_btn.pack(side='right', padx=10, ipady=8)

        # Login link
        login_frame = ttk.Frame(registration_container, style='TFrame')
        login_frame.pack(pady=10)

        login_label = ttk.Label(login_frame,
                              text="Already have an account? ",
                              foreground=TEXT_COLOR)
        login_label.pack(side='left')

        login_link = ttk.Label(login_frame,
                             text="Login",
                             foreground=SECONDARY_COLOR,
                             cursor="hand2")
        login_link.pack(side='left')
        login_link.bind("<Button-1>", lambda e: self.show_login())

    def create_form_field(self, parent, label_text, variable, row, name=None):
        """Create a form field with label and entry"""
        label = ttk.Label(parent, text=label_text, font=('Segoe UI', 10, 'bold'))
        label.grid(row=row, column=0, sticky='w', padx=5, pady=10)

        entry = ttk.Entry(parent, textvariable=variable, width=30)
        # Use winfo_class to set a tag for identification instead of name
        if name:
            entry.winfo_class()  # Just to access the widget
            entry.bindtags((name,) + entry.bindtags())
        entry.grid(row=row, column=1, sticky='w', padx=5, pady=10)

        return entry

    def create_decorative_background(self, parent_frame, title_text, subtitle_text):
        """Create a decorative background for the left frame"""
        try:
            # Get the current window height (more reliable than screen_height for resizing)
            window_height = self.root.winfo_height()

            # Create a canvas for the background
            canvas = tk.Canvas(parent_frame, bg=PRIMARY_COLOR, highlightthickness=0)
            canvas.pack(fill='both', expand=True)

            # Store the canvas in an instance variable for later updates
            self.bg_canvas = canvas

            # Create gradient effect - will be redrawn on resize
            self.draw_gradient_background(canvas, parent_frame.winfo_width(), window_height)

            # Try to load the logo if it exists
            if os.path.exists("logo.png"):
                # Load and resize the logo - size proportional to window
                logo_size = int(min(self.screen_width, window_height) * 0.15)  # 15% of smaller dimension
                logo_img = Image.open("logo.png")
                logo_img = logo_img.resize((logo_size, logo_size), Image.LANCZOS)

                # Convert to PhotoImage
                self.logo_photo = ImageTk.PhotoImage(logo_img)

                # Display the logo - centered in left panel
                self.logo_id = canvas.create_image(
                    parent_frame.winfo_width() // 2,
                    window_height // 4,  # Position at 1/4 of window height
                    image=self.logo_photo
                )

                # Add application name - positioned below logo
                title_font_size = max(14, int(min(self.screen_width, window_height) * 0.018))  # Scale font size with minimum
                self.app_title_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,  # Centered horizontally
                    window_height // 2,  # Middle of window
                    text="Scoliosis Detection System",
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white'
                )

                # Add tagline - positioned below title
                subtitle_font_size = max(10, int(min(self.screen_width, window_height) * 0.01))  # Scale font size with minimum
                self.tagline_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,  # Centered horizontally
                    window_height // 2 + title_font_size + 10,  # Below title
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )

                # Add page-specific text
                self.subtitle_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,  # Centered horizontally
                    window_height * 0.75,  # 3/4 down the window
                    text=subtitle_text,
                    font=('Segoe UI', subtitle_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )
            else:
                # If logo doesn't exist, create a text-only design
                # Add application name - positioned in the middle
                title_font_size = max(16, int(min(self.screen_width, window_height) * 0.022))
                self.app_title_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,
                    window_height // 3,
                    text=title_text,
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )

                # Add tagline
                subtitle_font_size = max(10, int(min(self.screen_width, window_height) * 0.01))
                self.tagline_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,
                    window_height // 2 + 30,
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )

                # Add page-specific text
                self.subtitle_id = canvas.create_text(
                    parent_frame.winfo_width() // 2,  # Centered horizontally
                    window_height * 0.75,  # 3/4 down the window
                    text=subtitle_text,
                    font=('Segoe UI', subtitle_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )

            # Store the parent frame for resize events
            self.bg_parent_frame = parent_frame

        except Exception as e:
            print(f"Error creating decorative background: {e}")
            # Fallback to simple background
            left_bg = tk.Frame(parent_frame, bg=PRIMARY_COLOR)
            left_bg.pack(fill='both', expand=True)

            # Calculate font size based on screen dimensions
            title_font_size = max(16, int(min(self.screen_width, self.root.winfo_height()) * 0.022))
            subtitle_font_size = max(10, int(min(self.screen_width, self.root.winfo_height()) * 0.01))

            app_name = tk.Label(left_bg, text=title_text,
                              font=('Segoe UI', title_font_size, 'bold'),
                              bg=PRIMARY_COLOR, fg='white')
            app_name.pack(pady=(self.root.winfo_height()//3, 20))

            # Add tagline
            tagline = tk.Label(left_bg, text="Advanced X-ray Analysis & Reporting",
                             font=('Segoe UI', subtitle_font_size),
                             bg=PRIMARY_COLOR, fg='white')
            tagline.pack()

            # Add page-specific text
            subtitle = tk.Label(left_bg, text=subtitle_text,
                              font=('Segoe UI', subtitle_font_size, 'bold'),
                              bg=PRIMARY_COLOR, fg='white')
            subtitle.pack(pady=(50, 0))

    def draw_gradient_background(self, canvas, width, height):
        """Draw a gradient background on the canvas"""
        # Clear existing items if any
        canvas.delete("gradient")

        # Create gradient effect
        for i in range(height):
            # Gradient from PRIMARY_COLOR to a slightly lighter shade
            r = int(44 + (i/height) * 20)  # 2c -> 44 in decimal
            g = int(62 + (i/height) * 20)  # 3e -> 62 in decimal
            b = int(80 + (i/height) * 20)  # 50 -> 80 in decimal
            color = f'#{r:02x}{g:02x}{b:02x}'
            canvas.create_line(0, i, width, i, fill=color, tags="gradient")

    def login(self):
        """Handle login button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get()

        if not username or not password:
            messagebox.showerror("Login Error", "Please enter both username and password.")
            return

        # First try JSON-based authentication
        if username in self.users:
            stored_password = self.users[username]["password"]
            if self.hash_password(password) == stored_password:
                # Login successful
                messagebox.showinfo("Login Successful", f"Welcome, {self.users[username]['name']}!")
                self.launch_main_application(username)
                return

        # If JSON auth fails, try SQLite (for backward compatibility)
        try:
            # Connect to SQLite database
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Try to find user in registration table
            cursor.execute('SELECT * FROM registration WHERE username = ? and password = ?',
                         (username, password))
            result = cursor.fetchall()

            # If not found, try admin_registration table
            if not result:
                cursor.execute('SELECT * FROM admin_registration WHERE username = ? and password = ?',
                             (username, password))
                result = cursor.fetchall()

            conn.close()

            if result:
                # Login successful with SQLite
                messagebox.showinfo("Login Successful", "Welcome to the Scoliosis Detection System!")

                # Add user to JSON database for future logins
                self.users[username] = {
                    "password": self.hash_password(password),
                    "name": result[0][0] if result[0][0] else username,  # Use Fullname if available
                    "role": "admin" if result[0][0] == "admin_registration" else "user"
                }
                self.save_users()

                self.launch_main_application(username)
                return
        except Exception as e:
            print(f"SQLite authentication error: {e}")

        # If all authentication methods fail
        messagebox.showerror("Login Error", "Invalid username or password.")

    def forgot_password(self, event=None):
        """Handle forgot password link click"""
        username = self.username_var.get().strip()

        if not username:
            messagebox.showerror("Error", "Please enter your username first.")
            return

        if username in self.users:
            # In a real application, this would send a password reset email
            # For this demo, we'll just reset to a default password
            new_password = "Password123!"
            self.users[username]["password"] = self.hash_password(new_password)
            self.save_users()

            messagebox.showinfo("Password Reset",
                              f"Password has been reset to: {new_password}\n\n"
                              "Please change it after logging in.")
        else:
            messagebox.showerror("Error", "Username not found.")

    def validate_password(self, password):
        """Validate password strength"""
        # Check if password meets requirements
        if len(password) < 6:
            return False, "Password must be at least 6 characters long"

        if not any(char.isdigit() for char in password):
            return False, "Password must contain at least one number"

        if not any(char.isupper() for char in password):
            return False, "Password must contain at least one uppercase letter"

        if not any(char in '!@#$%^&*()_+-=[]{}|;:,.<>?/~`' for char in password):
            return False, "Password must contain at least one special character"

        return True, "Password is valid"

    def validate_email(self, email):
        """Validate email format"""
        regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(regex, email) is not None

    def register(self):
        """Handle registration button click"""
        # Get form values
        fullname = self.fullname_var.get().strip()
        address = self.address_var.get().strip()
        username = self.reg_username_var.get().strip()
        email = self.email_var.get().strip()
        phone = self.phone_var.get().strip()
        gender = self.gender_var.get()
        age = self.age_var.get().strip()
        password = self.reg_password_var.get()
        confirm_password = self.confirm_password_var.get()

        # Validate inputs
        if not fullname:
            messagebox.showerror("Registration Error", "Please enter your full name")
            return

        if not address:
            messagebox.showerror("Registration Error", "Please enter your address")
            return

        if not username:
            messagebox.showerror("Registration Error", "Please enter a username")
            return

        if username in self.users:
            messagebox.showerror("Registration Error", "Username already exists. Please choose another one.")
            return

        if not email or not self.validate_email(email):
            messagebox.showerror("Registration Error", "Please enter a valid email address")
            return

        if not phone or not phone.isdigit() or len(phone) != 10:
            messagebox.showerror("Registration Error", "Please enter a valid 10-digit phone number")
            return

        if not gender:
            messagebox.showerror("Registration Error", "Please select your gender")
            return

        if not age or not age.isdigit() or int(age) <= 0 or int(age) > 120:
            messagebox.showerror("Registration Error", "Please enter a valid age (1-120)")
            return

        # Validate password
        is_valid, password_message = self.validate_password(password)
        if not is_valid:
            messagebox.showerror("Registration Error", password_message)
            return

        if password != confirm_password:
            messagebox.showerror("Registration Error", "Passwords do not match")
            return

        # Add user to JSON database
        self.users[username] = {
            "password": self.hash_password(password),
            "name": fullname,
            "role": "user",
            "email": email,
            "address": address,
            "phone": phone,
            "gender": "Male" if gender == 1 else "Female",
            "age": age
        }

        # Save user database
        self.save_users()

        # Also add to SQLite for backward compatibility
        try:
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Insert into registration table
            cursor.execute('''INSERT INTO registration
                           (Fullname, address, username, Email, Phoneno, Gender, age, password)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
                         (fullname, address, username, email, phone, gender, age, password))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error adding user to SQLite: {e}")

        # Show success message
        messagebox.showinfo("Registration Successful",
                          "Your account has been created successfully.\n"
                          "You can now log in with your credentials.")

        # Go to login page
        self.show_login()

    def create_window_controls(self):
        """Create window control buttons (minimize, maximize, close)"""
        # Create a frame for the window controls
        control_frame = tk.Frame(self.root, bg=PRIMARY_COLOR)
        control_frame.place(x=self.screen_width-110, y=10, width=100, height=30)

        # Minimize button
        minimize_btn = tk.Button(control_frame, text="—", bg=PRIMARY_COLOR, fg="white",
                               font=("Arial", 12), bd=0, padx=5,
                               command=self.minimize_window)
        minimize_btn.pack(side="left", padx=2)

        # Maximize/Restore button
        self.max_restore_btn = tk.Button(control_frame, text="□", bg=PRIMARY_COLOR, fg="white",
                                       font=("Arial", 12), bd=0, padx=5,
                                       command=self.toggle_maximize)
        self.max_restore_btn.pack(side="left", padx=2)

        # Close button
        close_btn = tk.Button(control_frame, text="×", bg=PRIMARY_COLOR, fg="white",
                            font=("Arial", 12), bd=0, padx=5,
                            command=self.close_window)
        close_btn.pack(side="left", padx=2)

        # Add hover effects
        for btn in [minimize_btn, self.max_restore_btn, close_btn]:
            btn.bind("<Enter>", lambda e, b=btn: b.config(bg=SECONDARY_COLOR))
            btn.bind("<Leave>", lambda e, b=btn: b.config(bg=PRIMARY_COLOR))

    def minimize_window(self):
        """Minimize the window"""
        self.root.iconify()

    def toggle_maximize(self):
        """Toggle between maximized and normal window state"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
            self.max_restore_btn.config(text="□")
        else:
            self.root.state('zoomed')
            self.max_restore_btn.config(text="❐")

    def close_window(self):
        """Close the window"""
        self.root.destroy()

    def on_window_resize(self, event=None):
        """Handle window resize events to adjust UI elements"""
        # Only process if this is a genuine resize (not just a minor adjustment)
        if event and (abs(event.width - self.screen_width) > 10 or abs(event.height - self.screen_height) > 10):
            # Update stored dimensions
            self.screen_width = event.width
            self.screen_height = event.height

            # Update window controls position
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Frame) and widget.winfo_x() > self.screen_width - 150:
                    widget.place(x=self.screen_width-110, y=10)
                    break

            # Update the decorative background if it exists
            try:
                if hasattr(self, 'bg_canvas') and hasattr(self, 'bg_parent_frame'):
                    # Get the current window height
                    window_height = self.root.winfo_height()
                    frame_width = self.bg_parent_frame.winfo_width()

                    # Redraw the gradient background
                    self.draw_gradient_background(self.bg_canvas, frame_width, window_height)

                    # Update logo position if it exists
                    if hasattr(self, 'logo_id'):
                        self.bg_canvas.coords(self.logo_id, frame_width // 2, window_height // 4)

                    # Update title position
                    if hasattr(self, 'app_title_id'):
                        self.bg_canvas.coords(self.app_title_id, frame_width // 2, window_height // 2)

                    # Update tagline position
                    if hasattr(self, 'tagline_id'):
                        title_font_size = max(14, int(min(self.screen_width, window_height) * 0.018))
                        self.bg_canvas.coords(self.tagline_id,
                                            frame_width // 2,
                                            window_height // 2 + title_font_size + 10)

                    # Update subtitle position
                    if hasattr(self, 'subtitle_id'):
                        self.bg_canvas.coords(self.subtitle_id,
                                            frame_width // 2,
                                            window_height * 0.75)
            except Exception as e:
                print(f"Error updating decorative background: {e}")

    def launch_main_application(self, username):
        """Launch the main application after successful login"""
        self.root.destroy()  # Close the auth window

        try:
            # Launch the main application with the username as a command-line argument
            subprocess.Popen([sys.executable, "GUI_Basic.py", username])
        except Exception as e:
            messagebox.showerror("Error", f"Error launching application: {str(e)}")


def main():
    """Main function to run the authentication system"""
    root = tk.Tk()

    # Set the application icon if available
    try:
        if os.path.exists("logo.png"):
            icon = tk.PhotoImage(file="logo.png")
            root.iconphoto(True, icon)
    except Exception as e:
        print(f"Error setting application icon: {e}")

    app = AuthSystem(root)
    root.mainloop()


if __name__ == "__main__":
    main()
