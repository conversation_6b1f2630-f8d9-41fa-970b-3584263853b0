import os
import datetime
import webbrowser
from PIL import Image
import subprocess
import sys

def generate_html_report(patient_name, diagnosis, image_path, recommendations=None):
    """Generate an HTML report for the patient"""
    if recommendations is None:
        if "Scoliosis Detected" in diagnosis:
            recommendations = [
                "Work with a physical therapist for customized exercises",
                "Schedule a follow-up appointment in 3 months",
                "Consider postural training to improve spinal alignment",
                "Maintain regular exercise routine focusing on core strength"
            ]
        else:
            recommendations = [
                "Maintain healthy diet and exercise routine",
                "Practice good posture during daily activities",
                "Schedule routine check-up in 6 months",
                "Stay hydrated for spinal disc health"
            ]
    
    # Create reports directory if it doesn't exist
    os.makedirs("reports", exist_ok=True)
    
    # Generate timestamp for the report filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create the report filename
    safe_name = patient_name.replace(' ', '_')
    html_filename = f"reports/{safe_name}_{timestamp}.html"
    
    # Get the confidence level (simulated)
    confidence = 95 if "Scoliosis Detected" in diagnosis else 92
    
    # Create the HTML content
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ray Analysis Report - {patient_name}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        .report-title {{
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
        }}
        .patient-info {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .diagnosis {{
            margin: 20px 0;
        }}
        .diagnosis-result {{
            font-size: 18px;
            font-weight: bold;
            color: {"#e74c3c" if "Scoliosis Detected" in diagnosis else "#2ecc71"};
        }}
        .confidence {{
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            margin: 15px 0;
        }}
        .confidence-bar {{
            background-color: #ecf0f1;
            height: 20px;
            border-radius: 10px;
            margin-top: 5px;
        }}
        .confidence-level {{
            background-color: {"#e74c3c" if "Scoliosis Detected" in diagnosis else "#2ecc71"};
            height: 20px;
            border-radius: 10px;
            width: {confidence}%;
        }}
        .recommendations {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }}
        .recommendations h3 {{
            color: #2c3e50;
            margin-top: 0;
        }}
        .recommendations ul {{
            padding-left: 20px;
        }}
        .image-section {{
            margin: 20px 0;
            text-align: center;
        }}
        .image-section img {{
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="report-title">X-ray Analysis Report</div>
        <div>Scoliosis Detection System</div>
        <div>Report Date: {datetime.datetime.now().strftime("%B %d, %Y")}</div>
    </div>
    
    <div class="patient-info">
        <h2>Patient Information</h2>
        <p><strong>Name:</strong> {patient_name}</p>
        <p><strong>Report ID:</strong> {timestamp}</p>
        <p><strong>Date of Analysis:</strong> {datetime.datetime.now().strftime("%B %d, %Y %H:%M")}</p>
    </div>
    
    <div class="diagnosis">
        <h2>Diagnosis</h2>
        <p class="diagnosis-result">{diagnosis.split('\n')[0]}</p>
        <p>{diagnosis.split('\n')[1] if '\n' in diagnosis else ''}</p>
        
        <div class="confidence">
            <p><strong>Confidence Level: {confidence}%</strong></p>
            <div class="confidence-bar">
                <div class="confidence-level"></div>
            </div>
        </div>
    </div>
    
    <div class="image-section">
        <h2>X-ray Image Analysis</h2>
        <img src="../{image_path}" alt="Patient X-ray Image">
    </div>
    
    <div class="recommendations">
        <h3>Recommendations</h3>
        <ul>
"""
    
    # Add recommendations
    for recommendation in recommendations:
        html_content += f"            <li>{recommendation}</li>\n"
    
    # Complete the HTML
    html_content += """        </ul>
    </div>
    
    <div class="footer">
        <p>This report was generated by the Scoliosis Detection System.</p>
        <p>For medical advice, please consult with your healthcare provider.</p>
    </div>
</body>
</html>
"""
    
    # Write the HTML file
    with open(html_filename, "w") as f:
        f.write(html_content)
    
    return html_filename

def generate_pdf_report(html_filename):
    """Generate a PDF report from the HTML report"""
    try:
        # Import required libraries
        import pdfkit
        
        # Get the PDF filename by replacing .html with .pdf
        pdf_filename = html_filename.replace(".html", ".pdf")
        
        # Convert HTML to PDF
        pdfkit.from_file(html_filename, pdf_filename)
        
        return pdf_filename
    except ImportError:
        # If pdfkit is not installed, try using weasyprint
        try:
            from weasyprint import HTML
            
            # Get the PDF filename by replacing .html with .pdf
            pdf_filename = html_filename.replace(".html", ".pdf")
            
            # Convert HTML to PDF
            HTML(html_filename).write_pdf(pdf_filename)
            
            return pdf_filename
        except ImportError:
            # If neither pdfkit nor weasyprint is installed, return None
            print("PDF generation requires pdfkit or weasyprint. Please install one of these packages.")
            return None

def open_html_report(html_filename):
    """Open the HTML report in the default web browser"""
    # Convert to absolute path
    abs_path = os.path.abspath(html_filename)
    
    # Open in browser
    webbrowser.open('file://' + abs_path)

def open_pdf_report(pdf_filename):
    """Open the PDF report in the default PDF viewer"""
    if os.path.exists(pdf_filename):
        # Open with default application
        if sys.platform == 'win32':
            os.startfile(pdf_filename)
        elif sys.platform == 'darwin':  # macOS
            subprocess.call(['open', pdf_filename])
        else:  # Linux
            subprocess.call(['xdg-open', pdf_filename])
        return True
    else:
        print(f"PDF file not found: {pdf_filename}")
        return False

if __name__ == "__main__":
    # Test the report generator
    patient_name = "John Doe"
    diagnosis = "Scoliosis Detected\nModerate curvature detected in the thoracic region."
    image_path = "sample_xray.jpg"
    
    html_file = generate_html_report(patient_name, diagnosis, image_path)
    print(f"HTML report generated: {html_file}")
    
    pdf_file = generate_pdf_report(html_file)
    if pdf_file:
        print(f"PDF report generated: {pdf_file}")
    
    # Open the reports
    open_html_report(html_file)
    if pdf_file:
        open_pdf_report(pdf_file)
