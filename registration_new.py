import os
import json
import hashlib
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageFilter, ImageEnhance
import sqlite3
import re
import sys
import subprocess

# Define color scheme (matching the main application)
PRIMARY_COLOR = "#2c3e50"  # Dark blue-gray
SECONDARY_COLOR = "#3498db"  # Bright blue
ACCENT_COLOR = "#e74c3c"  # Red
BG_COLOR = "#ecf0f1"  # Light gray
TEXT_COLOR = "#2c3e50"  # Dark blue-gray
HIGHLIGHT_COLOR = "#2ecc71"  # Green

# User database file for JSON-based authentication
USER_DB_FILE = "users.json"

class Registration:
    def __init__(self, root):
        """Initialize the registration window"""
        self.root = root
        self.root.title("Scoliosis Detection System - Registration")
        self.root.configure(bg=BG_COLOR)

        # Set window to full screen
        self.screen_width = self.root.winfo_screenwidth()
        self.screen_height = self.root.winfo_screenheight()

        # Configure for full screen
        self.root.geometry(f"{self.screen_width}x{self.screen_height}+0+0")
        self.root.resizable(True, True)

        # Add option to toggle fullscreen with F11 key
        self.is_fullscreen = True
        self.root.attributes('-fullscreen', self.is_fullscreen)

        # Bind F11 to toggle fullscreen and Escape to exit fullscreen
        self.root.bind("<F11>", self.toggle_fullscreen)
        self.root.bind("<Escape>", self.end_fullscreen)

        # Initialize variables first
        self.initialize_variables()

        # Create styles for widgets
        self.create_styles()

        # Load user database
        self.load_users()

        # Create the main layout (after variables are initialized)
        self.create_layout()

    def toggle_fullscreen(self, event=None):
        """Toggle between fullscreen and windowed mode"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)
        return "break"  # Prevent default behavior

    def end_fullscreen(self, event=None):
        """Exit fullscreen mode"""
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)
        return "break"  # Prevent default behavior

    def create_styles(self):
        """Create styles for the UI elements"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure basic styles
        self.style.configure('TFrame', background=BG_COLOR)
        self.style.configure('TLabel', background=BG_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', font=('Segoe UI', 10))
        self.style.configure('TEntry', font=('Segoe UI', 10))

        # Custom button styles
        self.style.configure('Register.TButton',
                           font=('Segoe UI', 12, 'bold'),
                           background=SECONDARY_COLOR,
                           foreground='white')

        self.style.configure('Cancel.TButton',
                           font=('Segoe UI', 12),
                           background=BG_COLOR,
                           foreground=TEXT_COLOR)

    def initialize_variables(self):
        """Initialize variables for registration form"""
        self.fullname_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.username_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.gender_var = tk.IntVar()
        self.age_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()

    def create_layout(self):
        """Create the main layout of the registration window"""
        # Create a two-column layout
        self.main_frame = ttk.Frame(self.root, style='TFrame')
        self.main_frame.pack(fill='both', expand=True)

        # Left column - Logo/Image - width is 40% of screen width
        self.left_width = int(self.screen_width * 0.4)
        self.left_frame = ttk.Frame(self.main_frame, style='TFrame', width=self.left_width)
        self.left_frame.pack(side='left', fill='both')
        self.left_frame.pack_propagate(False)  # Maintain width

        # Create decorative background
        self.create_decorative_background()

        # Right column - Registration form
        self.right_frame = ttk.Frame(self.main_frame, style='TFrame')
        self.right_frame.pack(side='right', fill='both', expand=True)

        # Registration form container (centered)
        self.registration_container = ttk.Frame(self.right_frame, style='TFrame')
        self.registration_container.place(relx=0.5, rely=0.5, anchor='center')

        # Registration form title
        title_label = ttk.Label(self.registration_container,
                              text="Create New Account",
                              font=('Segoe UI', 24, 'bold'),
                              foreground=PRIMARY_COLOR)
        title_label.pack(pady=(0, 5))

        # Registration form subtitle
        subtitle_label = ttk.Label(self.registration_container,
                                 text="Please fill in the information below",
                                 font=('Segoe UI', 10),
                                 foreground=TEXT_COLOR)
        subtitle_label.pack(pady=(0, 20))

        # Create a frame for the form fields
        form_frame = ttk.Frame(self.registration_container, style='TFrame')
        form_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Full Name field
        self.create_form_field(form_frame, "Full Name:", self.fullname_var, 0)

        # Address field
        self.create_form_field(form_frame, "Address:", self.address_var, 1)

        # Email field
        self.create_form_field(form_frame, "Email:", self.email_var, 2)

        # Phone field
        self.create_form_field(form_frame, "Phone:", self.phone_var, 3)

        # Gender field
        gender_label = ttk.Label(form_frame, text="Gender:", font=('Segoe UI', 10, 'bold'))
        gender_label.grid(row=4, column=0, sticky='w', padx=5, pady=10)

        gender_frame = ttk.Frame(form_frame, style='TFrame')
        gender_frame.grid(row=4, column=1, sticky='w', padx=5, pady=10)

        ttk.Radiobutton(gender_frame, text="Male", variable=self.gender_var, value=1).pack(side='left', padx=10)
        ttk.Radiobutton(gender_frame, text="Female", variable=self.gender_var, value=2).pack(side='left', padx=10)

        # Age field
        self.create_form_field(form_frame, "Age:", self.age_var, 5)

        # Username field
        self.create_form_field(form_frame, "Username:", self.username_var, 6)

        # Password field
        password_label = ttk.Label(form_frame, text="Password:", font=('Segoe UI', 10, 'bold'))
        password_label.grid(row=7, column=0, sticky='w', padx=5, pady=10)

        password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="•", width=30)
        password_entry.grid(row=7, column=1, sticky='w', padx=5, pady=10)

        # Password requirements
        password_req = ttk.Label(form_frame,
                               text="Password must contain at least 1 uppercase letter, 1 number, and 1 special character",
                               font=('Segoe UI', 8),
                               foreground='gray')
        password_req.grid(row=8, column=1, sticky='w', padx=5)

        # Confirm Password field
        confirm_label = ttk.Label(form_frame, text="Confirm Password:", font=('Segoe UI', 10, 'bold'))
        confirm_label.grid(row=9, column=0, sticky='w', padx=5, pady=10)

        confirm_entry = ttk.Entry(form_frame, textvariable=self.confirm_password_var, show="•", width=30)
        confirm_entry.grid(row=9, column=1, sticky='w', padx=5, pady=10)

        # Buttons frame
        buttons_frame = ttk.Frame(self.registration_container, style='TFrame')
        buttons_frame.pack(fill='x', pady=20)

        # Register button
        register_btn = ttk.Button(buttons_frame,
                                text="Register",
                                command=self.register,
                                style='Register.TButton',
                                width=20)
        register_btn.pack(side='left', padx=10, ipady=8)

        # Cancel button
        cancel_btn = ttk.Button(buttons_frame,
                              text="Cancel",
                              command=self.cancel,
                              style='Cancel.TButton',
                              width=20)
        cancel_btn.pack(side='right', padx=10, ipady=8)

        # Login link
        login_frame = ttk.Frame(self.registration_container, style='TFrame')
        login_frame.pack(pady=10)

        login_label = ttk.Label(login_frame,
                              text="Already have an account? ",
                              foreground=TEXT_COLOR)
        login_label.pack(side='left')

        login_link = ttk.Label(login_frame,
                             text="Login",
                             foreground=SECONDARY_COLOR,
                             cursor="hand2")
        login_link.pack(side='left')
        login_link.bind("<Button-1>", self.go_to_login)

    def create_form_field(self, parent, label_text, variable, row):
        """Create a form field with label and entry"""
        label = ttk.Label(parent, text=label_text, font=('Segoe UI', 10, 'bold'))
        label.grid(row=row, column=0, sticky='w', padx=5, pady=10)

        entry = ttk.Entry(parent, textvariable=variable, width=30)
        entry.grid(row=row, column=1, sticky='w', padx=5, pady=10)

        return entry

    def create_decorative_background(self):
        """Create a decorative background for the left frame"""
        try:
            # Try to load the logo if it exists
            if os.path.exists("logo.png"):
                # Create a canvas for the background
                canvas = tk.Canvas(self.left_frame, width=self.left_width, height=self.screen_height,
                                 bg=PRIMARY_COLOR, highlightthickness=0)
                canvas.pack(fill='both', expand=True)

                # Create gradient effect
                for i in range(self.screen_height):
                    # Gradient from PRIMARY_COLOR to a slightly lighter shade
                    r = int(44 + (i/self.screen_height) * 20)  # 2c -> 44 in decimal
                    g = int(62 + (i/self.screen_height) * 20)  # 3e -> 62 in decimal
                    b = int(80 + (i/self.screen_height) * 20)  # 50 -> 80 in decimal
                    color = f'#{r:02x}{g:02x}{b:02x}'
                    canvas.create_line(0, i, self.left_width, i, fill=color)

                # Load and resize the logo - size proportional to screen
                logo_size = int(min(self.screen_width, self.screen_height) * 0.2)  # 20% of smaller dimension
                logo_img = Image.open("logo.png")
                logo_img = logo_img.resize((logo_size, logo_size), Image.LANCZOS)

                # Apply a slight glow effect
                glow_img = logo_img.copy()
                glow_img = glow_img.filter(ImageFilter.GaussianBlur(10))
                enhancer = ImageEnhance.Brightness(glow_img)
                glow_img = enhancer.enhance(1.5)

                # Convert to PhotoImage
                logo_photo = ImageTk.PhotoImage(logo_img)

                # Display the logo - centered in left panel
                logo_x = self.left_width // 2
                logo_y = self.screen_height // 4  # Position at 1/4 of screen height
                canvas.create_image(logo_x, logo_y, image=logo_photo)
                canvas.image = logo_photo  # Keep a reference

                # Add application name - positioned below logo
                title_font_size = int(min(self.screen_width, self.screen_height) * 0.02)  # Scale font size
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height // 2,  # Middle of screen
                    text="Scoliosis Detection System",
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white'
                )

                # Add tagline - positioned below title
                subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)  # Scale font size
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height // 2 + title_font_size + 10,  # Below title
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )

                # Add registration text
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height * 0.75,  # 3/4 down the screen
                    text="Join our platform to access\nadvanced scoliosis detection tools",
                    font=('Segoe UI', subtitle_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )
            else:
                # If logo doesn't exist, create a gradient background
                canvas = tk.Canvas(self.left_frame, width=self.left_width, height=self.screen_height,
                                 bg=PRIMARY_COLOR, highlightthickness=0)
                canvas.pack(fill='both', expand=True)

                # Create gradient effect
                for i in range(self.screen_height):
                    # Gradient from PRIMARY_COLOR to a slightly lighter shade
                    r = int(44 + (i/self.screen_height) * 20)
                    g = int(62 + (i/self.screen_height) * 20)
                    b = int(80 + (i/self.screen_height) * 20)
                    color = f'#{r:02x}{g:02x}{b:02x}'
                    canvas.create_line(0, i, self.left_width, i, fill=color)

                # Add application name - positioned in the middle
                title_font_size = int(min(self.screen_width, self.screen_height) * 0.025)
                canvas.create_text(
                    self.left_width // 2,
                    self.screen_height // 3,
                    text="Scoliosis\nDetection\nSystem",
                    font=('Segoe UI', title_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )

                # Add tagline
                subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)
                canvas.create_text(
                    self.left_width // 2,
                    self.screen_height // 2 + 50,
                    text="Advanced X-ray Analysis & Reporting",
                    font=('Segoe UI', subtitle_font_size),
                    fill='white'
                )

                # Add registration text
                canvas.create_text(
                    self.left_width // 2,  # Centered horizontally
                    self.screen_height * 0.75,  # 3/4 down the screen
                    text="Join our platform to access\nadvanced scoliosis detection tools",
                    font=('Segoe UI', subtitle_font_size, 'bold'),
                    fill='white',
                    justify='center'
                )
        except Exception as e:
            print(f"Error creating decorative background: {e}")
            # Fallback to simple background
            left_bg = tk.Frame(self.left_frame, bg=PRIMARY_COLOR)
            left_bg.pack(fill='both', expand=True)

            # Calculate font size based on screen dimensions
            title_font_size = int(min(self.screen_width, self.screen_height) * 0.025)
            subtitle_font_size = int(min(self.screen_width, self.screen_height) * 0.012)

            app_name = tk.Label(left_bg, text="Scoliosis\nDetection\nSystem",
                              font=('Segoe UI', title_font_size, 'bold'),
                              bg=PRIMARY_COLOR, fg='white')
            app_name.pack(pady=(self.screen_height//3, 20))

            # Add tagline
            tagline = tk.Label(left_bg, text="Advanced X-ray Analysis & Reporting",
                             font=('Segoe UI', subtitle_font_size),
                             bg=PRIMARY_COLOR, fg='white')
            tagline.pack()

    def load_users(self):
        """Load user data from JSON file"""
        self.users = {}

        # First try to load from JSON file
        if os.path.exists(USER_DB_FILE):
            try:
                with open(USER_DB_FILE, 'r') as f:
                    self.users = json.load(f)
            except Exception as e:
                print(f"Error loading user database from JSON: {e}")

        # If no users found in JSON, try to load from SQLite
        if not self.users:
            try:
                # Connect to SQLite database
                conn = sqlite3.connect('evaluation.db')
                cursor = conn.cursor()

                # Create tables if they don't exist (for backward compatibility)
                cursor.execute("CREATE TABLE IF NOT EXISTS admin_registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")
                cursor.execute("CREATE TABLE IF NOT EXISTS registration"
                              "(Fullname TEXT, address TEXT, username TEXT, Email TEXT, Phoneno TEXT, Gender TEXT, age TEXT, password TEXT)")

                # Get users from admin_registration table
                cursor.execute("SELECT Fullname, username, password FROM admin_registration")
                admin_users = cursor.fetchall()

                # Get users from registration table
                cursor.execute("SELECT Fullname, username, password FROM registration")
                regular_users = cursor.fetchall()

                # Convert SQLite users to JSON format
                for fullname, username, password in admin_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "admin"
                        }

                for fullname, username, password in regular_users:
                    if username:  # Ensure username is not None or empty
                        self.users[username] = {
                            "password": self.hash_password(password) if password else "",
                            "name": fullname if fullname else username,
                            "role": "user"
                        }

                # Save the converted users to JSON
                if self.users:
                    self.save_users()

                conn.close()
            except Exception as e:
                print(f"Error loading users from SQLite: {e}")

    def save_users(self):
        """Save user data to JSON file"""
        try:
            with open(USER_DB_FILE, 'w') as f:
                json.dump(self.users, f, indent=4)
        except Exception as e:
            print(f"Error saving user database: {e}")

    def hash_password(self, password):
        """Hash a password using SHA-256"""
        if not password:
            return ""
        return hashlib.sha256(str(password).encode()).hexdigest()

    def validate_password(self, password):
        """Validate password strength"""
        # Check if password meets requirements
        if len(password) < 6:
            return False, "Password must be at least 6 characters long"

        if not any(char.isdigit() for char in password):
            return False, "Password must contain at least one number"

        if not any(char.isupper() for char in password):
            return False, "Password must contain at least one uppercase letter"

        if not any(char in '!@#$%^&*()_+-=[]{}|;:,.<>?/~`' for char in password):
            return False, "Password must contain at least one special character"

        return True, "Password is valid"

    def validate_email(self, email):
        """Validate email format"""
        regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(regex, email) is not None

    def register(self):
        """Handle registration button click"""
        # Get form values
        fullname = self.fullname_var.get().strip()
        address = self.address_var.get().strip()
        username = self.username_var.get().strip()
        email = self.email_var.get().strip()
        phone = self.phone_var.get().strip()
        gender = self.gender_var.get()
        age = self.age_var.get().strip()
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()

        # Validate inputs
        if not fullname:
            messagebox.showerror("Registration Error", "Please enter your full name")
            return

        if not address:
            messagebox.showerror("Registration Error", "Please enter your address")
            return

        if not username:
            messagebox.showerror("Registration Error", "Please enter a username")
            return

        if username in self.users:
            messagebox.showerror("Registration Error", "Username already exists. Please choose another one.")
            return

        if not email or not self.validate_email(email):
            messagebox.showerror("Registration Error", "Please enter a valid email address")
            return

        if not phone or not phone.isdigit() or len(phone) != 10:
            messagebox.showerror("Registration Error", "Please enter a valid 10-digit phone number")
            return

        if not gender:
            messagebox.showerror("Registration Error", "Please select your gender")
            return

        if not age or not age.isdigit() or int(age) <= 0 or int(age) > 120:
            messagebox.showerror("Registration Error", "Please enter a valid age (1-120)")
            return

        # Validate password
        is_valid, password_message = self.validate_password(password)
        if not is_valid:
            messagebox.showerror("Registration Error", password_message)
            return

        if password != confirm_password:
            messagebox.showerror("Registration Error", "Passwords do not match")
            return

        # Add user to JSON database
        self.users[username] = {
            "password": self.hash_password(password),
            "name": fullname,
            "role": "user",
            "email": email,
            "address": address,
            "phone": phone,
            "gender": "Male" if gender == 1 else "Female",
            "age": age
        }

        # Save user database
        self.save_users()

        # Also add to SQLite for backward compatibility
        try:
            conn = sqlite3.connect('evaluation.db')
            cursor = conn.cursor()

            # Insert into registration table
            cursor.execute('''INSERT INTO registration
                           (Fullname, address, username, Email, Phoneno, Gender, age, password)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
                         (fullname, address, username, email, phone, gender, age, password))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error adding user to SQLite: {e}")

        # Show success message
        messagebox.showinfo("Registration Successful",
                          "Your account has been created successfully.\n"
                          "You can now log in with your credentials.")

        # Go to login page
        self.go_to_login()

    def cancel(self):
        """Handle cancel button click"""
        if messagebox.askyesno("Cancel Registration", "Are you sure you want to cancel registration?"):
            self.go_to_login()

    def go_to_login(self, event=None):
        """Go back to the login page"""
        # Just close the registration window - the login window should still be open (minimized)
        self.root.destroy()

        # Try to find and restore the login window if it exists
        try:
            # This is a Windows-specific approach to find and restore the login window
            if sys.platform == 'win32':
                import win32gui
                def find_login_window(hwnd, param):
                    if "Scoliosis Detection System - Login" in win32gui.GetWindowText(hwnd):
                        win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE = 9
                        return False
                    return True
                win32gui.EnumWindows(find_login_window, None)
        except Exception as e:
            print(f"Error restoring login window: {e}")
            # If we can't restore the login window, create a new one
            try:
                subprocess.Popen([sys.executable, "login.py"])
            except Exception as e:
                print(f"Error launching login application: {e}")


def main():
    """Main function to run the registration system"""
    root = tk.Tk()

    # Set the application icon if available
    try:
        if os.path.exists("logo.png"):
            icon = tk.PhotoImage(file="logo.png")
            root.iconphoto(True, icon)
    except Exception as e:
        print(f"Error setting application icon: {e}")

    app = Registration(root)
    root.mainloop()


if __name__ == "__main__":
    main()
