"""
Auto-refresh functionality for the patient history tab.
This module provides functions to automatically refresh the patient history tab
when new data is added to the database.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

# Global variables
refresh_thread = None
stop_refresh_thread = False

def start_auto_refresh(refresh_function, interval=2.0):
    """
    Start a thread that automatically refreshes the patient history tab.
    
    Args:
        refresh_function: The function to call to refresh the patient history tab
        interval: The interval in seconds between refreshes (default: 2.0)
    """
    global refresh_thread, stop_refresh_thread
    
    # Stop any existing refresh thread
    stop_auto_refresh()
    
    # Reset the stop flag
    stop_refresh_thread = False
    
    # Define the refresh thread function
    def refresh_loop():
        while not stop_refresh_thread:
            # Call the refresh function
            refresh_function()
            
            # Sleep for the specified interval
            time.sleep(interval)
    
    # Create and start the refresh thread
    refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
    refresh_thread.start()
    
    print(f"Auto-refresh started with interval {interval} seconds")

def stop_auto_refresh():
    """Stop the auto-refresh thread if it's running."""
    global refresh_thread, stop_refresh_thread
    
    if refresh_thread and refresh_thread.is_alive():
        # Set the stop flag
        stop_refresh_thread = True
        
        # Wait for the thread to finish
        refresh_thread.join(timeout=1.0)
        
        print("Auto-refresh stopped")

def trigger_refresh(root, refresh_function):
    """
    Trigger a one-time refresh of the patient history tab.
    
    Args:
        root: The root Tkinter window
        refresh_function: The function to call to refresh the patient history tab
    """
    # Schedule the refresh function to be called in the main thread
    root.after(100, refresh_function)
    print("One-time refresh triggered")
