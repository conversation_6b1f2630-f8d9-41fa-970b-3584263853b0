import sqlite3
import os
import datetime
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
from user_database import get_current_user_id, is_authenticated

class PatientDatabase:
    """Class to handle patient history database operations"""

    def __init__(self, db_path="evaluation.db"):
        """Initialize the database connection"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.conn.cursor()
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")

    def create_tables(self):
        """Create necessary tables if they don't exist"""
        try:
            # Patients table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS patients (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    created_date TEXT NOT NULL,
                    last_visit TEXT NOT NULL,
                    user_id TEXT
                )
            ''')

            # Examinations table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS examinations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id TEXT NOT NULL,
                    exam_date TEXT NOT NULL,
                    diagnosis TEXT NOT NULL,
                    confidence REAL,
                    image_path TEXT,
                    report_path TEXT,
                    html_report_path TEXT,
                    notes TEXT,
                    user_id TEXT,
                    FOREIGN KEY (patient_id) REFERENCES patients (id)
                )
            ''')

            # Commit the changes
            self.conn.commit()
        except sqlite3.Error as e:
            print(f"Error creating tables: {e}")

    def add_patient(self, patient_id: str, name: str) -> bool:
        """Add a new patient to the database"""
        try:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None

            # Print debug information
            print(f"Adding patient with user_id: {user_id}")

            # Check if patient already exists
            self.cursor.execute("SELECT id FROM patients WHERE id = ?", (patient_id,))
            existing_patient = self.cursor.fetchone()

            if existing_patient:
                # Update last visit date
                self.cursor.execute(
                    "UPDATE patients SET last_visit = ? WHERE id = ?",
                    (current_date, patient_id)
                )

                # If the patient exists and we have a user_id, associate it with the current user
                if user_id:
                    try:
                        self.cursor.execute(
                            "UPDATE patients SET user_id = ? WHERE id = ?",
                            (user_id, patient_id)
                        )
                        print(f"Associated existing patient {patient_id} with user {user_id}")
                    except sqlite3.OperationalError as e:
                        print(f"Error updating patient user_id: {e}")
            else:
                # Insert new patient with user_id
                try:
                    self.cursor.execute(
                        "INSERT INTO patients (id, name, created_date, last_visit, user_id) VALUES (?, ?, ?, ?, ?)",
                        (patient_id, name, current_date, current_date, user_id)
                    )
                    print(f"Added new patient {patient_id} with user {user_id}")
                except sqlite3.OperationalError as e:
                    # If the user_id column doesn't exist, try without it
                    print(f"Error adding patient with user_id: {e}")
                    self.cursor.execute(
                        "INSERT INTO patients (id, name, created_date, last_visit) VALUES (?, ?, ?, ?)",
                        (patient_id, name, current_date, current_date)
                    )
                    print(f"Added new patient {patient_id} without user_id")

            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error adding patient: {e}")
            return False

    def add_examination(self, patient_id: str, diagnosis: str, confidence: float = None,
                       image_path: str = None, report_path: str = None,
                       html_report_path: str = None, notes: str = None) -> int:
        """Add a new examination record for a patient"""
        try:
            exam_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None
            print(f"Adding examination with user_id: {user_id}")

            # Update patient's last visit date
            self.cursor.execute(
                "UPDATE patients SET last_visit = ? WHERE id = ?",
                (exam_date, patient_id)
            )

            # Also update the user_id for this patient if it's not set
            if user_id:
                try:
                    self.cursor.execute(
                        "UPDATE patients SET user_id = ? WHERE id = ? AND (user_id IS NULL OR user_id = '')",
                        (user_id, patient_id)
                    )
                    if self.cursor.rowcount > 0:
                        print(f"Updated user_id for patient {patient_id} to {user_id}")
                except sqlite3.OperationalError as e:
                    print(f"Error updating patient user_id: {e}")

            # Insert examination record with user_id
            try:
                self.cursor.execute(
                    """INSERT INTO examinations
                       (patient_id, exam_date, diagnosis, confidence, image_path,
                        report_path, html_report_path, notes, user_id)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (patient_id, exam_date, diagnosis, confidence, image_path,
                     report_path, html_report_path, notes, user_id)
                )
                print(f"Added examination for patient {patient_id} with user {user_id}")
            except sqlite3.OperationalError as e:
                # If the user_id column doesn't exist, try without it
                print(f"Error adding examination with user_id: {e}")
                self.cursor.execute(
                    """INSERT INTO examinations
                       (patient_id, exam_date, diagnosis, confidence, image_path,
                        report_path, html_report_path, notes)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (patient_id, exam_date, diagnosis, confidence, image_path,
                     report_path, html_report_path, notes)
                )
                print(f"Added examination for patient {patient_id} without user_id")

            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding examination: {e}")
            return -1

    def patient_exists(self, patient_id: str) -> bool:
        """Check if a patient exists in the database"""
        try:
            self.cursor.execute("SELECT id FROM patients WHERE id = ?", (patient_id,))
            return self.cursor.fetchone() is not None
        except sqlite3.Error as e:
            print(f"Error checking patient existence: {e}")
            return False

    def get_patient(self, patient_id: str) -> Dict[str, Any]:
        """Get patient information by ID"""
        try:
            self.cursor.execute("SELECT * FROM patients WHERE id = ?", (patient_id,))
            patient = self.cursor.fetchone()
            if patient:
                return dict(patient)
            return {}
        except sqlite3.Error as e:
            print(f"Error getting patient: {e}")
            return {}

    def get_patient_by_name(self, name: str) -> List[Dict[str, Any]]:
        """Search for patients by name (partial match)"""
        try:
            self.cursor.execute("SELECT * FROM patients WHERE name LIKE ?", (f"%{name}%",))
            patients = self.cursor.fetchall()
            return [dict(patient) for patient in patients]
        except sqlite3.Error as e:
            print(f"Error searching patients: {e}")
            return []

    def get_all_patients(self) -> List[Dict[str, Any]]:
        """Get all patients in the database for the current user with examination counts"""
        try:
            # First check if user_id column exists in patients table
            self.cursor.execute("PRAGMA table_info(patients)")
            columns = [column[1] for column in self.cursor.fetchall()]
            has_user_id = 'user_id' in columns

            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None
            print(f"Getting patients for user: {user_id}")

            if is_authenticated() and has_user_id and user_id:
                # Get patients with examination counts and actual last examination date
                query = """
                    SELECT p.*,
                           COUNT(e.id) as examination_count,
                           MAX(e.exam_date) as actual_last_visit
                    FROM patients p
                    LEFT JOIN examinations e ON p.id = e.patient_id AND e.user_id = ?
                    WHERE p.user_id = ?
                    GROUP BY p.id, p.name, p.created_date, p.last_visit, p.user_id
                    ORDER BY COALESCE(MAX(e.exam_date), p.last_visit) DESC
                """
                self.cursor.execute(query, [user_id, user_id])
                patients = self.cursor.fetchall()

                # Convert to list of dictionaries with enhanced information
                enhanced_patients = []
                for patient in patients:
                    patient_dict = dict(patient)
                    # Use actual last examination date if available, otherwise use patient's last_visit
                    if patient_dict.get('actual_last_visit'):
                        patient_dict['last_visit'] = patient_dict['actual_last_visit']
                    enhanced_patients.append(patient_dict)

                print(f"Found {len(enhanced_patients)} patients for user {user_id}")
                return enhanced_patients
            elif not is_authenticated():
                # If not authenticated, return no patients
                print("No user authenticated, returning no patients")
                return []
            else:
                # If authenticated but no user_id column, return empty list for new users
                # This ensures new users don't see data that doesn't belong to them
                print("User authenticated but no user_id column or no user_id, returning no patients")
                return []
        except sqlite3.Error as e:
            print(f"Error getting all patients: {e}")
            return []

    def get_patient_examinations(self, patient_id: str) -> List[Dict[str, Any]]:
        """Get all examinations for a specific patient"""
        try:
            # First check if user_id column exists in examinations table
            self.cursor.execute("PRAGMA table_info(examinations)")
            columns = [column[1] for column in self.cursor.fetchall()]
            has_user_id = 'user_id' in columns

            query = "SELECT * FROM examinations WHERE patient_id = ?"
            params = [patient_id]

            # Filter by user_id if authenticated and column exists
            if is_authenticated() and has_user_id:
                user_id = get_current_user_id()
                query += " AND user_id = ?"
                params.append(user_id)

            query += " ORDER BY exam_date DESC"

            self.cursor.execute(query, params)
            examinations = self.cursor.fetchall()
            return [dict(exam) for exam in examinations]
        except sqlite3.Error as e:
            print(f"Error getting patient examinations: {e}")
            return []

    def get_examination(self, exam_id: int) -> Dict[str, Any]:
        """Get a specific examination by ID"""
        try:
            self.cursor.execute("SELECT * FROM examinations WHERE id = ?", (exam_id,))
            exam = self.cursor.fetchone()
            if exam:
                return dict(exam)
            return {}
        except sqlite3.Error as e:
            print(f"Error getting examination: {e}")
            return {}

    def update_examination_notes(self, exam_id: int, notes: str) -> bool:
        """Update the notes for an examination"""
        try:
            self.cursor.execute(
                "UPDATE examinations SET notes = ? WHERE id = ?",
                (notes, exam_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating examination notes: {e}")
            return False

    def delete_patient(self, patient_id: str) -> bool:
        """Delete a patient and all their examinations from the database"""
        try:
            # Get current user ID if authenticated
            user_id = get_current_user_id() if is_authenticated() else None

            if not user_id:
                print("No user authenticated, cannot delete patient")
                return False

            # First check if the patient belongs to the current user
            self.cursor.execute(
                "SELECT id FROM patients WHERE id = ? AND user_id = ?",
                (patient_id, user_id)
            )

            if not self.cursor.fetchone():
                print(f"Patient {patient_id} does not belong to user {user_id} or does not exist")
                return False

            # Begin a transaction
            self.conn.execute("BEGIN TRANSACTION")

            # Delete all examinations for this patient
            self.cursor.execute(
                "DELETE FROM examinations WHERE patient_id = ?",
                (patient_id,)
            )
            exams_deleted = self.cursor.rowcount

            # Delete the patient
            self.cursor.execute(
                "DELETE FROM patients WHERE id = ?",
                (patient_id,)
            )
            patient_deleted = self.cursor.rowcount

            # Commit the transaction
            self.conn.commit()

            print(f"Deleted patient {patient_id} and {exams_deleted} examinations")
            return patient_deleted > 0

        except sqlite3.Error as e:
            # Rollback in case of error
            self.conn.rollback()
            print(f"Error deleting patient: {e}")
            return False

    def get_recent_examinations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the most recent examinations across all patients for the current user"""
        try:
            # First check if user_id column exists in examinations table
            self.cursor.execute("PRAGMA table_info(examinations)")
            columns = [column[1] for column in self.cursor.fetchall()]
            has_user_id = 'user_id' in columns

            query = """SELECT e.*, p.name as patient_name
                       FROM examinations e
                       JOIN patients p ON e.patient_id = p.id"""
            params = []

            # Filter by user_id if authenticated and column exists
            if is_authenticated() and has_user_id:
                user_id = get_current_user_id()
                query += " WHERE e.user_id = ?"
                params.append(user_id)

            query += " ORDER BY e.exam_date DESC LIMIT ?"
            params.append(limit)

            self.cursor.execute(query, params)
            examinations = self.cursor.fetchall()
            return [dict(exam) for exam in examinations]
        except sqlite3.Error as e:
            print(f"Error getting recent examinations: {e}")
            return []

    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()

    def __del__(self):
        """Destructor to ensure the database connection is closed"""
        self.close()


# Create a singleton instance
_instance = None

def get_db_instance():
    """Get a singleton instance of the PatientDatabase"""
    global _instance
    if _instance is None:
        _instance = PatientDatabase()
    else:
        # Check if we need to refresh the connection
        try:
            # Test the connection by executing a simple query
            _instance.cursor.execute("SELECT 1")
        except (sqlite3.Error, sqlite3.ProgrammingError):
            # Connection is closed or invalid, create a new instance
            print("Database connection was closed, creating a new instance")
            _instance.close()
            _instance = PatientDatabase()
    return _instance


if __name__ == "__main__":
    # Test the database
    db = get_db_instance()

    # Add a test patient
    patient_id = "TEST123"
    db.add_patient(patient_id, "Test Patient")

    # Add a test examination
    db.add_examination(
        patient_id=patient_id,
        diagnosis="Scoliosis Detected",
        confidence=95.5,
        notes="Initial examination"
    )

    # Get patient information
    patient = db.get_patient(patient_id)
    print(f"Patient: {patient}")

    # Get patient examinations
    exams = db.get_patient_examinations(patient_id)
    print(f"Examinations: {exams}")

    print("Database test completed successfully")
