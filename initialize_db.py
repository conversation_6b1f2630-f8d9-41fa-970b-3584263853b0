import os
import sqlite3
import shutil

def initialize_database():
    """Initialize the database with the correct schema"""
    # Remove the existing database file if it exists
    if os.path.exists('evaluation.db'):
        os.remove('evaluation.db')
        print("Removed existing database file")

    # Create a new database file
    conn = sqlite3.connect('evaluation.db')
    cursor = conn.cursor()

    print("Creating new database with correct schema...")

    # Create patients table with user_id column
    cursor.execute('''
        CREATE TABLE patients (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            created_date TEXT NOT NULL,
            last_visit TEXT NOT NULL,
            user_id TEXT
        )
    ''')
    print("Created patients table with user_id column")

    # Create examinations table with user_id column
    cursor.execute('''
        CREATE TABLE examinations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date TEXT NOT NULL,
            diagnosis TEXT NOT NULL,
            confidence REAL,
            image_path TEXT,
            report_path TEXT,
            html_report_path TEXT,
            notes TEXT,
            user_id TEXT,
            FOREIGN KEY (patient_id) REFERENCES patients (id)
        )
    ''')
    print("Created examinations table with user_id column")

    # Create reports table with user_id column
    cursor.execute('''
        CREATE TABLE reports (
            patient_id TEXT,
            patient_name TEXT,
            timestamp TEXT,
            diagnosis TEXT,
            image_path TEXT,
            report_path TEXT,
            user_id TEXT
        )
    ''')
    print("Created reports table with user_id column")

    # Test inserting a patient with user_id
    cursor.execute(
        "INSERT INTO patients (id, name, created_date, last_visit, user_id) VALUES (?, ?, ?, ?, ?)",
        ("P1001", "Test Patient", "2023-01-01 12:00:00", "2023-01-01 12:00:00", "test_user")
    )

    # Test querying the patient
    cursor.execute("SELECT * FROM patients WHERE user_id = ?", ("test_user",))
    patient = cursor.fetchone()

    if patient:
        print(f"Successfully inserted and retrieved patient with user_id")
    else:
        print("Failed to retrieve patient with user_id")

    # Test updating the patient
    cursor.execute(
        "UPDATE patients SET user_id = ? WHERE id = ?",
        ("updated_user", "P1001")
    )

    # Test querying the updated patient
    cursor.execute("SELECT * FROM patients WHERE user_id = ?", ("updated_user",))
    updated_patient = cursor.fetchone()

    if updated_patient:
        print(f"Successfully updated and retrieved patient with user_id")
    else:
        print("Failed to update patient with user_id")

    # Clean up the test data
    cursor.execute("DELETE FROM patients WHERE id = ?", ("P1001",))

    conn.commit()
    conn.close()

    # Make a backup of the database file
    shutil.copy('evaluation.db', 'evaluation_backup.db')
    print("Created backup of database file")

    print("Database initialization complete")

if __name__ == "__main__":
    initialize_database()
